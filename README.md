---
title: FxThrone Trading Platform
emoji: 📊
colorFrom: green
colorTo: green
sdk: fastapi
sdk_version: 0.115.8
app_file: app.py
pinned: false
---

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference

# FxThrone: Prop Trading Platform

A comprehensive prop trading solution that provides traders with account management, order processing, and integration with trading platforms. The application offers a secure authentication system, order management, and MyFXBook API integration for tracking trading performance.

## Features

- **User Authentication**: Secure login and registration system with email verification
- **Order Management**: Process and track trading orders with different account sizes and challenge types
- **MyFXBook Integration**: Connect and fetch data from MyFXBook accounts
- **Cloud Storage**: Image uploads for transaction verification using Cloudinary
- **Database Integration**: PostgreSQL database for storing user and order information
- **Email Notifications**: Automated email notifications with customized templates
- **Secure API**: JWT-based authentication for all API endpoints
- **Trading Account Management**: Automated account creation and management

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file with your configuration:
   ```
   DATABASE_URL=your_postgresql_connection_string
   JWT_SECRET_KEY=your_jwt_secret_key
   CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
   CLOUDINARY_API_KEY=your_cloudinary_api_key
   CLOUDINARY_API_SECRET=your_cloudinary_api_secret
   ```

## Usage

### Starting the Server

```bash
# Start the server with uvicorn
uvicorn hello:app --host 0.0.0.0 --port 8000

# Or use the provided script
python run.py
```

The API will be available at http://localhost:8000/
The API documentation will be available at http://localhost:8000/docs

### Running with Docker

You can also run the application using Docker:

1. Build the Docker image:
   ```bash
   docker build -t fundedwhales .
   ```

2. Run the Docker container:
   ```bash
   docker run -p 8000:8000 --env-file .env fundedwhales
   ```

3. Or use Docker Compose:
   ```bash
   docker-compose up
   ```

The API will be available at http://localhost:8000/
The API documentation will be available at http://localhost:8000/docs

## API Usage

The platform can be accessed via a REST API built with FastAPI.

### API Endpoints

1. **User Registration**
   ```
   POST /auth/register
   ```
   Request body:
   ```json
   {
     "username": "trader1",
     "email": "<EMAIL>",
     "password": "securepassword",
     "name": "John Trader",
     "phone_no": "+**********",
     "country": "United States",
     "address": "123 Trading St, New York"
   }
   ```
   Response:
   ```json
   {
     "id": 1,
     "username": "trader1",
     "email": "<EMAIL>",
     "name": "John Trader",
     "country": "United States",
     "phone_no": "+**********",
     "address": "123 Trading St, New York"
   }
   ```

2. **User Login**
   ```
   POST /auth/login
   ```
   Request body (form data):
   ```
   username: <EMAIL>
   password: securepassword
   ```
   Response:
   ```json
   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "is_verified": true
   }
   ```

3. **Create Order**
   ```
   POST /order/order
   ```
   Request body (form data with optional file upload):
   ```
   email: <EMAIL>
   challenge_type: standard
   account_size: 10000
   platform: mt4
   payment_method: crypto
   txid: tx123456789
   image: [file upload]
   ```
   Response:
   ```json
   {
     "id": 1,
     "username": "trader1",
     "email": "<EMAIL>",
     "challenge_type": "standard",
     "account_size": "10000",
     "platform": "mt4",
     "payment_method": "crypto",
     "txid": "tx123456789",
     "images": [
       {
         "id": 1,
         "image_url": "https://res.cloudinary.com/...",
         "cloudinary_public_id": "orders/image123",
         "created_at": "2023-06-15T10:30:00Z"
       }
     ]
   }
   ```

4. **Fetch MyFXBook Accounts**
   ```
   POST /myfxbook/fetch_accounts
   ```
   Request body (form data):
   ```
   email: <EMAIL>
   password: securepassword
   ```
   Response:
   ```json
   {
     "session": "abc123xyz",
     "accounts": [
       {
         "id": "12345",
         "name": "Trading Account 1",
         "gain": 15.6,
         "drawdown": 5.2,
         "balance": 10560.25
       }
     ]
   }
   ```

### Example API Usage with cURL

```bash
# Register a new user
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"trader1","email":"<EMAIL>","password":"securepassword","name":"John Trader","phone_no":"+**********","country":"United States","address":"123 Trading St, New York"}'

# Login
curl -X POST http://localhost:8000/auth/login \
  -F "username=<EMAIL>" \
  -F "password=securepassword"

# Create an order (without image)
curl -X POST http://localhost:8000/order/order \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "email=<EMAIL>" \
  -F "challenge_type=standard" \
  -F "account_size=10000" \
  -F "platform=mt4" \
  -F "payment_method=crypto" \
  -F "txid=tx123456789"
```

### Example API Usage with Python

```python
import requests

# Register a new user
response = requests.post(
    "http://localhost:8000/auth/register",
    json={
        "username": "trader1",
        "email": "<EMAIL>",
        "password": "securepassword",
        "name": "John Trader",
        "phone_no": "+**********",
        "country": "United States",
        "address": "123 Trading St, New York"
    }
)
print(response.json())

# Login
response = requests.post(
    "http://localhost:8000/auth/login",
    data={
        "username": "<EMAIL>",
        "password": "securepassword"
    }
)
token = response.json()["access_token"]

# Create an order (without image)
response = requests.post(
    "http://localhost:8000/order/order",
    headers={"Authorization": f"Bearer {token}"},
    data={
        "email": "<EMAIL>",
        "challenge_type": "standard",
        "account_size": "10000",
        "platform": "mt4",
        "payment_method": "crypto",
        "txid": "tx123456789"
    }
)
print(response.json())
```

## Architecture

The application follows a modular architecture:

1. **Authentication Module**: Handles user registration, login, and JWT token management
2. **Order Management Module**: Processes trading orders and manages account creation
3. **MyFXBook Integration**: Connects to MyFXBook API for account data retrieval
4. **Database Layer**: SQLModel ORM for PostgreSQL database interactions
5. **Email Service**: Handles email notifications and verifications
6. **Cloud Storage**: Manages file uploads to Cloudinary
7. **API Layer**: FastAPI endpoints exposing all functionality
8. **Docker Deployment**: Containerized application for easy deployment

## Technology Stack

- **Backend**: FastAPI
- **Database**: PostgreSQL with SQLModel ORM
- **Authentication**: JWT with bcrypt password hashing
- **Email**: SMTP integration for notifications
- **Cloud Storage**: Cloudinary
- **Deployment**: Docker containerization
- **Package Management**: UV (Python package installer)

## Environment Variables

The application requires the following environment variables:

- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET_KEY`: Secret key for JWT token generation
- `ALGORITHM`: JWT algorithm (default: HS256)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: JWT token expiration time
- `CLOUDINARY_CLOUD_NAME`: Cloudinary cloud name
- `CLOUDINARY_API_KEY`: Cloudinary API key
- `CLOUDINARY_API_SECRET`: Cloudinary API secret

## License

MIT

---

*This Hugging Face Space is maintained by FxThrone. For support or inquiries, <NAME_EMAIL>*