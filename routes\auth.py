from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from sqlmodel import SQLModel, Field, Session, select
from schemas.user import UserCreate, UserResponse, UserWithVerification, Token
from models.user import User, generate_referral_code
from models.user_verification import UserVerification
from models.points_transaction import PointsTransaction
from auth import get_password_hash, verify_password, create_access_token, get_current_user
from db import get_session
from fastapi.responses import StreamingResponse
import io
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from pydantic import EmailStr
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from utils.referral import validate_referral_code, award_referral_points, generate_unique_referral_code
import secrets
from datetime import datetime, timedelta
import random
from templates.email_templates import (
    create_welcome_email,
    create_base_email_template
)

auth_router = APIRouter(prefix = "/auth")

# Function to send email
def send_email(to_email, subject, body):
    from_email = "<EMAIL>"  # Replace with your Hostinger email
    from_password = "Fxthrone@123"   # Use your email password

    msg = MIMEMultipart()
    msg["From"] = from_email
    msg["To"] = to_email
    msg["Subject"] = subject
    msg.attach(MIMEText(body, "html"))

    # Use Hostinger's SMTP server
    with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:  # SSL on port 465
        server.login(from_email, from_password)
        server.sendmail(from_email, to_email, msg.as_string())

# Email Verification Token Model
class EmailVerificationToken(SQLModel, table=True):
    __tablename__ = "email_verification_tokens"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")  # Fixed: Changed from "users.id" to "user.id"
    code: str = Field(index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime = Field()
    is_used: bool = Field(default=False)

# Password Reset Token Model
class PasswordResetToken(SQLModel, table=True):
    __tablename__ = "password_reset_tokens"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    token: str = Field(index=True, unique=True)  # Ensure tokens are unique
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime = Field()
    is_used: bool = Field(default=False)

def generate_verification_code():
    # Generate a 6-digit code
    return ''.join([str(random.randint(0, 9)) for _ in range(6)])

def generate_reset_token():
    # Generate a secure random token
    return secrets.token_urlsafe(32)

def send_password_reset_email(to_email: str, reset_token: str):
    reset_link = f"https://fundedwhales.com/reset-password?token={reset_token}"
    subject = "Password Reset - FundedWhales"
    
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Password Reset Request</h3>
    
    <p style="margin: 0 0 20px;">We received a request to reset your password. If you didn't make this request, you can safely ignore this email.</p>
    
    <p style="margin: 0 0 20px;">To reset your password, click the button below:</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <p style="margin: 0; color: #4A5568; word-break: break-all;">
            <a href="{reset_link}" style="color: #4A90E2; text-decoration: none;">{reset_link}</a>
        </p>
    </div>
    
    <p style="margin: 20px 0; color: #718096;">This password reset link will expire in 1 hour for security reasons.</p>
    
    <p style="margin: 20px 0; color: #2C5282; font-weight: 600;">If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
    """
    
    html_content = create_base_email_template(
        "Password Reset Request",
        content,
        "Reset Password",
        reset_link
    )
    
    send_email(to_email, subject, html_content)

def send_verification_email(to_email: str, verification_code: str):
    subject = "🔐 Verify Your Email - FundedWhales"

    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #4299E1 0%, #3182CE 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">🔐</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Verify Your Email Address</h3>
    </div>

    <p style="margin: 0 0 30px; font-size: 16px; color: #2D3748; text-align: center;">We're excited to have you join FundedWhales! Please verify your email address to complete your registration.</p>

    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 3px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0; text-align: center; position: relative; overflow: hidden;">
        <div style="position: absolute; top: -20px; right: -20px; width: 40px; height: 40px; background: rgba(66, 153, 225, 0.2); border-radius: 50%;"></div>
        <div style="position: absolute; bottom: -15px; left: -15px; width: 30px; height: 30px; background: rgba(66, 153, 225, 0.3); border-radius: 50%;"></div>

        <h4 style="color: #1A365D; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">Your Verification Code</h4>
        <div style="background: #FFFFFF; border: 2px dashed #4299E1; border-radius: 12px; padding: 20px; margin: 20px 0; position: relative; z-index: 2;">
            <code style="font-size: 36px; font-weight: 800; color: #1A365D; letter-spacing: 8px; font-family: 'Courier New', monospace;">{verification_code}</code>
        </div>
        <p style="margin: 15px 0 0 0; color: #4A5568; font-size: 14px;">Enter this code in the verification form</p>
    </div>

    <div style="background: #FFF5F5; border: 1px solid #FEB2B2; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <div style="display: flex; align-items: center;">
            <div style="background: #E53E3E; color: white; border-radius: 50%; width: 24px; height: 24px; line-height: 24px; text-align: center; margin-right: 12px; font-size: 14px;">⚠</div>
            <div>
                <p style="margin: 0; color: #C53030; font-weight: 600; font-size: 14px;">Security Notice</p>
                <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 13px;">This code expires in 24 hours. Never share it with anyone.</p>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0; color: #4A5568; font-size: 14px;">Having trouble? Contact our support team for assistance.</p>
    </div>
    """

    html_content = create_base_email_template(
        "Verify Your Email Address",
        content,
        None,
        None,
        "#4299E1"
    )

    send_email(to_email, subject, html_content)

def send_welcome_email(user: User):
    subject = "🎉 Welcome to FxThrone - Your Trading Journey Begins!"
    # Only include referral code if user has access enabled
    referral_code = user.referral_code if user.referral_code_access_enabled else None
    html_content = create_welcome_email(user.username, referral_code)
    send_email(user.email, subject, html_content)

# Signup Endpoint
@auth_router.post("/signup", response_model=UserWithVerification)
def signup(user_create: UserCreate, session: Session = Depends(get_session)):
    try:
        # Check if user already exists
        user_exists = session.exec(select(User).where(User.email == user_create.email)).first()
        if user_exists:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered")

        # Validate referral code if provided
        referring_user = None
        if user_create.referral_code:
            referring_user = validate_referral_code(user_create.referral_code, session)
            if not referring_user:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid referral code")

        # Hash the password and create user
        hashed_password = get_password_hash(user_create.password)
        new_user = User(
            username=user_create.username,
            email=user_create.email,
            hashed_password=hashed_password,
            name=user_create.name,
            country=user_create.country,
            phone_no=user_create.phone_no,
            address=user_create.address,
            referral_code=None,  # No automatic referral code generation
            referred_by=referring_user.id if referring_user else None,
            points_balance=0,
            is_admin=False,  # Default to non-admin
            referral_code_access_enabled=False  # Default to no referral code access
        )
        session.add(new_user)
        session.commit()
        session.refresh(new_user)

        # Award referral points to the referring user
        if referring_user:
            try:
                award_referral_points(referring_user, new_user, session, points=50)
                session.commit()
                print(f"Awarded 50 points to user {referring_user.username} for referring {new_user.username}")
            except Exception as e:
                print(f"Error awarding referral points: {str(e)}")
                # Continue even if referral points fail

        try:
            # Create verification record
            verification = UserVerification(user_id=new_user.id)
            session.add(verification)
            session.commit()

            # Generate verification code
            verification_code = generate_verification_code()
            verification_token = EmailVerificationToken(
                user_id=new_user.id,
                code=verification_code,
                expires_at=datetime.utcnow() + timedelta(hours=24)
            )
            session.add(verification_token)
            session.commit()

            # Send verification email with code
            try:
                send_verification_email(new_user.email, verification_code)
            except Exception as e:
                print(f"Error sending verification email: {str(e)}")
                # Continue even if email fails, as user is created

            # Send welcome email
            try:
                send_welcome_email(new_user)
            except Exception as e:
                print(f"Error sending welcome email: {str(e)}")
                # Continue even if email fails, as user is created

        except Exception as e:
            print(f"Error in verification process: {str(e)}")
            # Continue as user is created successfully

        # Create response with verification status
        user_with_verification = UserWithVerification.model_validate(new_user)
        user_with_verification.is_verified = False
        return user_with_verification

    except Exception as e:
        session.rollback()
        print(f"Error in signup process: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during signup. Please try again."
        )

@auth_router.post("/verify-email")
def verify_email(email: str, code: str, session: Session = Depends(get_session)):
    # Get user
    user = session.exec(select(User).where(User.email == email)).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Get verification token
    token = session.exec(
        select(EmailVerificationToken)
        .where(EmailVerificationToken.user_id == user.id)
        .where(EmailVerificationToken.code == code)
        .where(EmailVerificationToken.is_used == False)
        .where(EmailVerificationToken.expires_at > datetime.utcnow())
    ).first()

    if not token:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid or expired verification code")

    # Update verification status
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == user.id)).first()
    if not verification:
        verification = UserVerification(user_id=user.id)
        session.add(verification)

    verification.is_verified = True
    verification.verified_at = datetime.utcnow()

    # Mark token as used
    token.is_used = True

    session.commit()
    return {"message": "Email verified successfully"}

@auth_router.get("/user/verification-status/{user_id}")
def get_verification_status(user_id: int, session: Session = Depends(get_session)):
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == user_id)).first()
    if not verification:
        return {"is_verified": False}
    return {"is_verified": verification.is_verified}

@auth_router.post("/resend-verification")
def resend_verification(email: str, session: Session = Depends(get_session)):
    # Get user
    user = session.exec(select(User).where(User.email == email)).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if email is already verified
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == user.id)).first()
    is_verified = verification.is_verified if verification else False

    if is_verified:
        raise HTTPException(status_code=400, detail="Email is already verified")

    # Generate new verification code
    verification_code = generate_verification_code()
    verification_token = EmailVerificationToken(
        user_id=user.id,
        code=verification_code,
        expires_at=datetime.utcnow() + timedelta(hours=24)
    )
    session.add(verification_token)
    session.commit()

    # Send verification email with new code
    send_verification_email(user.email, verification_code)

    return {"message": "Verification code sent successfully"}

@auth_router.put("/user/{user_id}/update-password", status_code=status.HTTP_200_OK)
def update_password(user_id: int, new_password: str, session: Session = Depends(get_session)):
    # Fetch the user by ID
    user = session.exec(select(User).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Verify if the new password is the same as the existing password
    if verify_password(new_password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="New password cannot be the same as the current password")

    # Hash the new password and update the existing column
    user.hashed_password = get_password_hash(new_password)
    session.add(user)
    session.commit()
    session.refresh(user)

    return {"message": "Password updated successfully"}

@auth_router.get("/user/{user_id}/password", status_code=status.HTTP_200_OK)
def get_password(user_id: int, session: Session = Depends(get_session)):
        # Fetch the user by ID
        user = session.exec(select(User).where(User.id == user_id)).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        return {"hashed_password": user.hashed_password}

# Login Endpoint
#NOTE: You have to tell the frontend developer that he has to send the email in the key of username and should ask from the user the email but put it against the username key in the header.
@auth_router.post("/login", response_model=Token)
def login(form_data: Annotated[OAuth2PasswordRequestForm, Depends(OAuth2PasswordRequestForm)], session: Session = Depends(get_session)):
    db_user = session.exec(select(User).where(User.email == form_data.username)).first()
    if not db_user or not verify_password(form_data.password, db_user.hashed_password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid credentials")

    # Check if email is verified by querying the UserVerification table
    # We still check verification status but don't block login
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == db_user.id)).first()
    is_verified = verification.is_verified if verification else False

    # No longer blocking unverified users from logging in

    # Create JWT token
    access_token = create_access_token(data={"sub": str(db_user.id)})
    return {"access_token": access_token, "token_type": "bearer", "is_verified": is_verified}
# Token Refresh Endpoint
@auth_router.post("/token/refresh", response_model=Token)
def refresh_token(current_user: User = Depends(get_current_user), session: Session = Depends(get_session)):
    # Check verification status
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == current_user.id)).first()
    is_verified = verification.is_verified if verification else False

    access_token = create_access_token(data={"sub": str(current_user.id)})
    return {"access_token": access_token, "token_type": "bearer", "is_verified": is_verified}


    # Get All Users Endpoint
@auth_router.get("/users", response_model=List[UserWithVerification])
def get_all_users(session: Session = Depends(get_session)):
    users = session.exec(select(User)).all()
    result = []
    for user in users:
        verification = session.exec(select(UserVerification).where(UserVerification.user_id == user.id)).first()
        # Create a new UserWithVerification instance
        user_with_verification = UserWithVerification.model_validate(user)
        user_with_verification.is_verified = verification.is_verified if verification else False
        result.append(user_with_verification)
    return result


    # Get Current User Details Endpoint
@auth_router.get("/user/me", response_model=UserWithVerification)
def get_current_user_details(current_user: User = Depends(get_current_user), session: Session = Depends(get_session)):
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == current_user.id)).first()
    # Create a new UserWithVerification instance
    user_with_verification = UserWithVerification.model_validate(current_user)
    user_with_verification.is_verified = verification.is_verified if verification else False

    # Only show referral code if user has access enabled
    if not current_user.referral_code_access_enabled:
        user_with_verification.referral_code = None

    return user_with_verification

# Forgot Password Endpoint - Request password reset
@auth_router.post("/forgot-password")
def forgot_password(email: str, session: Session = Depends(get_session)):
    # Find user by email
    user = session.exec(select(User).where(User.email == email)).first()
    if not user:
        # For security reasons, don't reveal that the email doesn't exist
        # Just return success message as if we sent the email
        return {"message": "If your email is registered, you will receive a password reset link."}

    # Generate reset token
    reset_token = generate_reset_token()

    # Create password reset token record
    token_record = PasswordResetToken(
        user_id=user.id,
        token=reset_token,
        expires_at=datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour
    )
    session.add(token_record)
    session.commit()

    # Send password reset email
    try:
        send_password_reset_email(user.email, reset_token)
    except Exception as e:
        print(f"Error sending password reset email: {str(e)}")
        # Continue even if email fails, as token is created

    return {"message": "If your email is registered, you will receive a password reset link."}

# Reset Password Endpoint - Reset password using token
@auth_router.post("/reset-password")
def reset_password(token: str, new_password: str, session: Session = Depends(get_session)):
    # Find valid token first
    token_record = session.exec(
        select(PasswordResetToken)
        .where(PasswordResetToken.token == token)
        .where(PasswordResetToken.is_used == False)
        .where(PasswordResetToken.expires_at > datetime.utcnow())
    ).first()

    if not token_record:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid or expired token")

    # Find user based on the token's user_id
    user = session.exec(select(User).where(User.id == token_record.user_id)).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Verify if the new password is the same as the existing password
    if verify_password(new_password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="New password cannot be the same as the current password")

    # Update password
    user.hashed_password = get_password_hash(new_password)

    # Mark token as used
    token_record.is_used = True

    session.add(user)
    session.add(token_record)
    session.commit()

    return {"message": "Password has been reset successfully"}

