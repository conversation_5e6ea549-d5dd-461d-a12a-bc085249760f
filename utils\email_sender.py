"""
Professional email sending utility with anti-spam optimizations
"""
import smtplib
import uuid
import re
from html import unescape
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.utils import formatdate


class ProfessionalEmailSender:
    """
    Professional email sender with anti-spam optimizations and proper headers
    """
    
    def __init__(self, smtp_server="smtp.hostinger.com", smtp_port=465):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.from_email = "<EMAIL>"
        self.from_password = "Fxthrone@123"
        self.from_name = "FxThrone Support Team"
        self.company_domain = "fxthrone.com"
    
    def _create_plain_text_version(self, html_content):
        """
        Convert HTML content to plain text for better deliverability
        """
        # Remove HTML tags
        plain_text = re.sub('<[^<]+?>', '', html_content)
        # Decode HTML entities
        plain_text = unescape(plain_text)
        # Clean up whitespace
        plain_text = re.sub(r'\n\s*\n', '\n\n', plain_text.strip())
        # Remove excessive whitespace
        plain_text = re.sub(r' +', ' ', plain_text)
        return plain_text
    
    def _add_professional_headers(self, msg, to_email, subject):
        """
        Add professional headers to prevent spam filtering and improve deliverability
        """
        # Essential headers for deliverability
        msg["From"] = f"{self.from_name} <{self.from_email}>"
        msg["To"] = to_email
        msg["Subject"] = subject
        msg["Reply-To"] = self.from_email
        msg["Return-Path"] = self.from_email

        # Anti-spam headers
        msg["X-Mailer"] = "FxThrone Email System v1.0"
        msg["X-Priority"] = "3"
        msg["X-MSMail-Priority"] = "Normal"
        msg["Importance"] = "Normal"
        msg["MIME-Version"] = "1.0"

        # Authentication and reputation headers
        msg["X-Sender"] = self.from_email
        msg["X-Original-Sender"] = self.from_email
        msg["Sender"] = self.from_email

        # Organization headers for professionalism
        msg["Organization"] = "FxThrone Prop Trading"
        msg["X-Organization"] = "FxThrone"

        # Message ID for tracking and authenticity
        msg["Message-ID"] = f"<{uuid.uuid4()}@{self.company_domain}>"

        # Date header (required by RFC)
        msg["Date"] = formatdate(localtime=True)

        # List headers (helps with spam filters and compliance)
        msg["List-Unsubscribe"] = f"<mailto:{self.from_email}?subject=Unsubscribe>"
        msg["List-Unsubscribe-Post"] = "List-Unsubscribe=One-Click"

        # Additional headers for better deliverability
        msg["X-Auto-Response-Suppress"] = "OOF, DR, RN, NRN, AutoReply"
        msg["X-Entity-ID"] = f"fxthrone-{uuid.uuid4().hex[:8]}"

        # Content classification
        msg["X-Content-Class"] = "urn:content-classes:message"

        return msg
    
    def send_email(self, to_email, subject, html_body, email_type="notification"):
        """
        Send professional email with anti-spam optimizations
        
        Args:
            to_email (str): Recipient email address
            subject (str): Email subject line
            html_body (str): HTML email content
            email_type (str): Type of email for categorization
        
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Create multipart message for both HTML and plain text
            msg = MIMEMultipart('alternative')
            
            # Add professional headers
            msg = self._add_professional_headers(msg, to_email, subject)
            
            # Add email type header for internal tracking
            msg["X-Email-Type"] = email_type
            
            # Create plain text version for better deliverability
            plain_text = self._create_plain_text_version(html_body)
            
            # Create message parts
            text_part = MIMEText(plain_text, "plain", "utf-8")
            html_part = MIMEText(html_body, "html", "utf-8")
            
            # Attach both versions (plain text first for better compatibility)
            msg.attach(text_part)
            msg.attach(html_part)
            
            # Send email using SMTP SSL
            with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port) as server:
                server.login(self.from_email, self.from_password)
                server.sendmail(self.from_email, to_email, msg.as_string())
            
            print(f"Email sent successfully to {to_email} - Subject: {subject}")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            print(f"SMTP Authentication failed: {str(e)}")
            return False
        except smtplib.SMTPRecipientsRefused as e:
            print(f"Recipient refused: {str(e)}")
            return False
        except smtplib.SMTPServerDisconnected as e:
            print(f"SMTP server disconnected: {str(e)}")
            return False
        except Exception as e:
            print(f"Email sending failed: {str(e)}")
            return False
    
    def send_welcome_email(self, to_email, username, referral_code=None):
        """Send welcome email"""
        from templates.email_templates import create_welcome_email
        subject = "Welcome to FxThrone - Your Trading Journey Begins!"
        html_content = create_welcome_email(username, referral_code)
        return self.send_email(to_email, subject, html_content, "welcome")
    
    def send_order_confirmation(self, to_email, username, order_id, challenge_type, account_size, platform):
        """Send order confirmation email"""
        from templates.email_templates import create_order_confirmation_email
        subject = "Order Confirmation - FxThrone"
        html_content = create_order_confirmation_email(username, order_id, challenge_type, account_size, platform)
        return self.send_email(to_email, subject, html_content, "order_confirmation")
    
    def send_verification_email(self, to_email, verification_code):
        """Send email verification"""
        from templates.email_templates import create_base_email_template
        subject = "Verify Your Email Address - FxThrone"
        
        content = f"""
        <div style="text-align: center; margin-bottom: 30px;">
            <h3 style="color: #172554; margin: 0; font-size: 20px; font-weight: 600;">Verify Your Email Address</h3>
        </div>
        
        <p style="margin: 0 0 20px;">Thank you for registering with FxThrone. To complete your registration, please verify your email address using the code below:</p>
        
        <div style="background: #f8f9fa; border: 2px solid #007BFF; border-radius: 6px; padding: 20px; margin: 20px 0; text-align: center;">
            <h4 style="color: #007BFF; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">Verification Code</h4>
            <div style="background: #ffffff; border: 1px solid #007BFF; border-radius: 4px; padding: 15px; margin: 10px 0;">
                <code style="font-size: 24px; font-weight: 700; color: #172554; letter-spacing: 2px;">{verification_code}</code>
            </div>
            <p style="margin: 10px 0 0 0; color: #6c757d; font-size: 14px;">This code will expire in 24 hours.</p>
        </div>
        
        <p style="margin: 20px 0;">If you didn't create an account with FxThrone, please ignore this email.</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">Having trouble? Contact our support team for assistance.</p>
        </div>
        """
        
        html_content = create_base_email_template("Email Verification", content, None, None, "#007BFF")
        return self.send_email(to_email, subject, html_content, "verification")


# Create global instance for easy import
email_sender = ProfessionalEmailSender()
