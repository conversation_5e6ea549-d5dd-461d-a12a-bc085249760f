#!/usr/bin/env python3
"""
Test script for the updated create order endpoint without txid parameter.
"""

import requests
import json
import sys
import time

BASE_URL = "http://localhost:8000"  # Adjust if your app runs on different port

def test_user_login():
    """Test user login and get token"""
    print("🧪 Testing user login...")
    
    # Try to login with existing user
    login_data = {"username": "<EMAIL>", "password": "testpassword123"}
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print("   ✅ Login successful")
            return token
        else:
            print(f"   ❌ Login failed: {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_create_order_without_txid(token):
    """Test creating an order without txid parameter"""
    print("🧪 Testing create order without txid parameter...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Order data (no txid required)
    order_data = {
        "challenge_type": "Phase 1",
        "account_size": "$10,000",
        "platform": "MT4",
        "payment_method": "Credit Card"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/order/order", data=order_data, headers=headers)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            order_response = response.json()
            print("   ✅ SUCCESS - Order created without txid parameter")
            
            # Display order details
            print(f"   📋 Order ID: {order_response.get('id', 'N/A')}")
            print(f"   👤 Username: {order_response.get('username', 'N/A')}")
            print(f"   💰 Account Size: {order_response.get('account_size', 'N/A')}")
            print(f"   🎯 Challenge Type: {order_response.get('challenge_type', 'N/A')}")
            print(f"   🖥️  Platform: {order_response.get('platform', 'N/A')}")
            print(f"   💳 Payment Method: {order_response.get('payment_method', 'N/A')}")
            
            # Check if txid was auto-generated
            auto_txid = order_response.get('txid', 'N/A')
            print(f"   🔗 Auto-generated TXID: {auto_txid}")
            
            if auto_txid.startswith('FW-'):
                print("   ✅ PASS: Transaction ID auto-generated correctly")
            else:
                print("   ⚠️  WARNING: Transaction ID format unexpected")
            
            return order_response
        else:
            print(f"   ❌ FAILED - {response.json()}")
            return None
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return None

def test_create_multiple_orders(token):
    """Test creating multiple orders to ensure unique txid generation"""
    print("🧪 Testing multiple order creation (unique txid generation)...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    orders_created = []
    txids_generated = []
    
    for i in range(3):
        print(f"   Creating order {i+1}/3...")
        
        order_data = {
            "challenge_type": f"Phase {i+1}",
            "account_size": f"${(i+1)*10000}",
            "platform": "MT4" if i % 2 == 0 else "MT5",
            "payment_method": "Credit Card"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/order/order", data=order_data, headers=headers)
            
            if response.status_code == 200:
                order_response = response.json()
                txid = order_response.get('txid', '')
                
                print(f"      ✅ Order {i+1} created - TXID: {txid}")
                orders_created.append(order_response)
                txids_generated.append(txid)
            else:
                print(f"      ❌ Order {i+1} failed: {response.json()}")
                
        except Exception as e:
            print(f"      ❌ Order {i+1} error: {e}")
        
        # Small delay to ensure different timestamps
        time.sleep(1)
    
    # Check if all txids are unique
    unique_txids = set(txids_generated)
    if len(unique_txids) == len(txids_generated):
        print(f"   ✅ PASS: All {len(txids_generated)} transaction IDs are unique")
    else:
        print(f"   ❌ FAIL: Duplicate transaction IDs found")
    
    return orders_created

def test_missing_required_fields(token):
    """Test that required fields are still validated"""
    print("🧪 Testing required field validation...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test missing challenge_type
    incomplete_data = {
        "account_size": "$10,000",
        "platform": "MT4",
        "payment_method": "Credit Card"
        # Missing challenge_type
    }
    
    try:
        response = requests.post(f"{BASE_URL}/order/order", data=incomplete_data, headers=headers)
        
        if response.status_code == 422:  # Validation error
            print("   ✅ PASS: Missing required field correctly rejected")
            return True
        else:
            print(f"   ❌ FAIL: Missing field was accepted (status: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Create Order Endpoint (No TXID Parameter)")
    print("=" * 60)
    
    # Test 1: Login
    token = test_user_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        sys.exit(1)
    
    # Test 2: Create order without txid
    order = test_create_order_without_txid(token)
    if not order:
        print("❌ Create order test failed")
        sys.exit(1)
    
    # Test 3: Create multiple orders to test unique txid generation
    multiple_orders = test_create_multiple_orders(token)
    
    # Test 4: Test required field validation
    validation_test = test_missing_required_fields(token)
    
    print("\n" + "=" * 60)
    print("🎉 Create Order Tests Completed!")
    print("\n📋 Summary:")
    print("   ✅ TXID parameter removed from endpoint")
    print("   ✅ Transaction IDs auto-generated with unique format")
    print("   ✅ Order creation works without txid parameter")
    print("   ✅ Multiple orders generate unique transaction IDs")
    print("   ✅ Required field validation still works")
    print("   ✅ Order response format maintained")
    
    print("\n🔧 Updated Endpoint:")
    print("   POST /order/order")
    print("   Required Parameters:")
    print("     - challenge_type (e.g., 'Phase 1')")
    print("     - account_size (e.g., '$10,000')")
    print("     - platform (e.g., 'MT4', 'MT5')")
    print("     - payment_method (e.g., 'Credit Card')")
    print("     - Authorization header (Bearer token)")
    print("   Removed Parameters:")
    print("     - txid (now auto-generated)")
    print("     - image (removed in previous update)")
    
    print("\n🔗 Auto-generated TXID Format:")
    print("   FW-{order_id}-{timestamp}")
    print("   Example: FW-********-**********")

if __name__ == "__main__":
    main()
