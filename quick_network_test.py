#!/usr/bin/env python3
"""
Quick network diagnostic for database connection issues.
"""

import socket
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_dns_resolution():
    """Test DNS resolution for the database hostname"""
    print("🔍 Testing DNS Resolution...")
    
    hostname = "ep-super-shadow-aeu6oyxe-pooler.c-2.us-east-2.aws.neon.tech"
    
    try:
        ip_address = socket.gethostbyname(hostname)
        print(f"   ✅ DNS Resolution successful: {hostname} → {ip_address}")
        return True
    except socket.gaierror as e:
        print(f"   ❌ DNS Resolution failed: {e}")
        print(f"   🌐 Cannot resolve hostname: {hostname}")
        return False

def test_tcp_connection():
    """Test TCP connection to database server"""
    print("\n🌐 Testing TCP Connection...")
    
    hostname = "ep-super-shadow-aeu6oyxe-pooler.c-2.us-east-2.aws.neon.tech"
    port = 5432
    
    try:
        # Create socket and test connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10 second timeout
        
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ TCP connection successful to {hostname}:{port}")
            return True
        else:
            print(f"   ❌ TCP connection failed to {hostname}:{port} (error: {result})")
            return False
            
    except Exception as e:
        print(f"   ❌ TCP connection error: {e}")
        return False

def test_internet_connectivity():
    """Test general internet connectivity"""
    print("\n🌍 Testing Internet Connectivity...")
    
    test_hosts = [
        ("google.com", 80),
        ("*******", 53),
        ("*******", 53)
    ]
    
    for host, port in test_hosts:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ Can connect to {host}:{port}")
                return True
            else:
                print(f"   ❌ Cannot connect to {host}:{port}")
        except Exception as e:
            print(f"   ❌ Error connecting to {host}:{port}: {e}")
    
    print("   ❌ No internet connectivity detected")
    return False

def provide_solutions():
    """Provide solutions based on test results"""
    print("\n💡 Solutions to Try:")
    print("=" * 40)
    
    print("1. 🔄 Network Reset:")
    print("   - Run as Administrator in Command Prompt:")
    print("     ipconfig /flushdns")
    print("     ipconfig /release")
    print("     ipconfig /renew")
    
    print("\n2. 📡 Change DNS Servers:")
    print("   - Network Settings → Change adapter options")
    print("   - Right-click connection → Properties")
    print("   - IPv4 Properties → Use these DNS servers:")
    print("     Primary: *******")
    print("     Secondary: *******")
    
    print("\n3. 🔥 Check Firewall/Antivirus:")
    print("   - Temporarily disable Windows Firewall")
    print("   - Add Python.exe to firewall exceptions")
    print("   - Check antivirus blocking network connections")
    
    print("\n4. 🌐 Try Different Network:")
    print("   - Use mobile hotspot")
    print("   - Try from different location/network")
    
    print("\n5. ⏰ Wait and Retry:")
    print("   - Neon database might be temporarily down")
    print("   - Check Neon status page")
    print("   - Try again in a few minutes")
    
    print("\n6. 🔧 Alternative Database:")
    print("   - Consider using local PostgreSQL for development")
    print("   - Or try a different cloud database provider")

def main():
    """Main diagnostic function"""
    print("🚀 Quick Network Diagnostic for Database Connection")
    print("=" * 60)
    
    # Test internet connectivity first
    internet_ok = test_internet_connectivity()
    
    if not internet_ok:
        print("\n❌ No internet connectivity detected!")
        print("   Please check your internet connection first.")
        provide_solutions()
        return
    
    # Test DNS resolution
    dns_ok = test_dns_resolution()
    
    # Test TCP connection (only if DNS works)
    tcp_ok = False
    if dns_ok:
        tcp_ok = test_tcp_connection()
    
    # Summary and solutions
    print("\n" + "=" * 60)
    print("📋 Diagnostic Summary:")
    print(f"   Internet Connectivity: {'✅ OK' if internet_ok else '❌ FAILED'}")
    print(f"   DNS Resolution: {'✅ OK' if dns_ok else '❌ FAILED'}")
    print(f"   TCP Connection: {'✅ OK' if tcp_ok else '❌ FAILED'}")
    
    if dns_ok and tcp_ok:
        print("\n🎉 Network connectivity is working!")
        print("   The database connection issue might be:")
        print("   - Authentication problem")
        print("   - Database server temporarily unavailable")
        print("   - SSL/TLS configuration issue")
    else:
        provide_solutions()

if __name__ == "__main__":
    main()
