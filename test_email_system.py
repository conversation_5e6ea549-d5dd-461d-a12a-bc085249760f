"""
Comprehensive email system testing script for FxThrone
Tests all email templates and validates deliverability
"""
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_email_templates():
    """Test all email templates for proper rendering"""
    print("🧪 Testing Email Templates")
    print("=" * 50)
    
    try:
        from templates.email_templates import (
            create_welcome_email,
            create_order_confirmation_email,
            create_referral_bonus_email,
            create_points_milestone_email,
            create_giveaway_entry_email,
            create_affiliate_commission_email,
            create_pass_notification_email,
            create_fail_notification_email,
            create_live_account_email,
            create_certificate_email
        )
        
        # Test data
        test_username = "<PERSON>"
        test_email = "<EMAIL>"
        test_order_id = 12345
        
        # Test welcome email
        print("✅ Testing welcome email...")
        welcome_html = create_welcome_email(test_username, "REF123")
        assert "FxThrone" in welcome_html
        assert test_username in welcome_html
        assert "REF123" in welcome_html
        # Check for both logos
        assert "svg" in welcome_html.lower()  # FxThrone SVG logo
        assert "cloudinary.com" in welcome_html  # Cloudinary logo
        assert "180_3_2_bphm2n.svg" in welcome_html  # Professional SVG logo file
        
        # Test order confirmation
        print("✅ Testing order confirmation email...")
        order_html = create_order_confirmation_email(test_username, test_order_id, "Phase 1", "$50,000", "MT4")
        assert "Order Confirmation" in order_html
        assert str(test_order_id) in order_html
        assert "$50,000" in order_html
        
        # Test referral bonus
        print("✅ Testing referral bonus email...")
        referral_html = create_referral_bonus_email(test_username, "Jane Smith", "<EMAIL>", 50)
        assert test_username in referral_html
        assert "Jane Smith" in referral_html
        assert "50" in referral_html
        
        # Test points milestone
        print("✅ Testing points milestone email...")
        points_html = create_points_milestone_email(test_username, 250, 250)
        assert test_username in points_html
        assert "250" in points_html
        
        # Test giveaway entry
        print("✅ Testing giveaway entry email...")
        giveaway_html = create_giveaway_entry_email(test_username, "Monthly Giveaway", "iPhone 15", 50000, "December 31, 2024")
        assert test_username in giveaway_html
        assert "Monthly Giveaway" in giveaway_html
        
        # Test affiliate commission
        print("✅ Testing affiliate commission email...")
        affiliate_html = create_affiliate_commission_email(test_username, "Referred User", 100, test_order_id, 500.0, 1)
        assert test_username in affiliate_html
        assert "100" in affiliate_html
        
        # Test pass notification
        print("✅ Testing pass notification email...")
        pass_html = create_pass_notification_email(test_username, test_order_id, 15.5, 8500.75)
        assert test_username in pass_html
        assert "15.5" in pass_html
        
        # Test fail notification
        print("✅ Testing fail notification email...")
        fail_html = create_fail_notification_email(test_username, test_order_id, "Maximum drawdown exceeded")
        assert test_username in fail_html
        assert "drawdown" in fail_html
        
        # Test live account
        print("✅ Testing live account email...")
        live_html = create_live_account_email(test_username, test_order_id, "live.server.com", "123456", "password123", 80)
        assert test_username in live_html
        assert "123456" in live_html
        
        # Test certificate
        print("✅ Testing certificate email...")
        cert_html = create_certificate_email(test_username, test_order_id, "Phase 1", "$50,000", "CERT-001", "2024-01-15", 5250.75)
        assert test_username in cert_html
        assert "CERT-001" in cert_html
        
        print("\n✅ All email templates passed validation!")
        return True
        
    except Exception as e:
        print(f"❌ Email template test failed: {str(e)}")
        return False

def test_email_sender():
    """Test the professional email sender utility"""
    print("\n📧 Testing Email Sender Utility")
    print("=" * 50)
    
    try:
        from utils.email_sender import ProfessionalEmailSender
        
        # Create email sender instance
        sender = ProfessionalEmailSender()
        
        # Test plain text conversion
        html_content = "<h1>Test</h1><p>This is a <strong>test</strong> email.</p>"
        plain_text = sender._create_plain_text_version(html_content)
        
        assert "Test" in plain_text
        assert "test email" in plain_text
        assert "<h1>" not in plain_text
        
        print("✅ Plain text conversion working correctly")
        
        # Test header creation
        from email.mime.multipart import MIMEMultipart
        msg = MIMEMultipart()
        msg = sender._add_professional_headers(msg, "<EMAIL>", "Test Subject")
        
        required_headers = [
            "From", "To", "Subject", "Message-ID", "Date", 
            "X-Mailer", "Reply-To", "Return-Path"
        ]
        
        for header in required_headers:
            assert header in msg
            print(f"✅ Header '{header}' present")
        
        print("\n✅ Email sender utility passed validation!")
        return True
        
    except Exception as e:
        print(f"❌ Email sender test failed: {str(e)}")
        return False

def test_spam_score_factors():
    """Test for common spam trigger factors"""
    print("\n🛡️ Testing Spam Score Factors")
    print("=" * 50)
    
    try:
        from templates.email_templates import create_welcome_email
        
        # Generate a sample email
        sample_email = create_welcome_email("Test User", "REF123")
        
        # Check for spam triggers
        spam_triggers = [
            "URGENT", "ACT NOW", "LIMITED TIME", "CLICK HERE NOW",
            "MAKE MONEY FAST", "GUARANTEED", "NO RISK", "FREE MONEY",
            "WINNER", "CONGRATULATIONS!!!", "CASH BONUS"
        ]
        
        found_triggers = []
        for trigger in spam_triggers:
            if trigger.lower() in sample_email.lower():
                found_triggers.append(trigger)
        
        if found_triggers:
            print(f"⚠️  Found potential spam triggers: {', '.join(found_triggers)}")
        else:
            print("✅ No common spam triggers found")
        
        # Check for good practices
        good_practices = [
            ("Professional sender name", "FxThrone" in sample_email),
            ("Proper HTML structure", "<!DOCTYPE html" in sample_email),
            ("Unsubscribe information", "contact" in sample_email.lower()),
            ("Company information", "FxThrone" in sample_email),
            ("Professional language", "Dear" in sample_email or "Thank you" in sample_email)
        ]
        
        for practice, check in good_practices:
            if check:
                print(f"✅ {practice}: Present")
            else:
                print(f"⚠️  {practice}: Missing")
        
        print("\n✅ Spam score factor analysis complete!")
        return len(found_triggers) == 0
        
    except Exception as e:
        print(f"❌ Spam score test failed: {str(e)}")
        return False

def generate_test_report():
    """Generate a comprehensive test report"""
    print("\n📊 Generating Test Report")
    print("=" * 50)
    
    report = f"""
# FxThrone Email System Test Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Test Results Summary
- Email Templates: {'✅ PASSED' if test_email_templates() else '❌ FAILED'}
- Email Sender Utility: {'✅ PASSED' if test_email_sender() else '❌ FAILED'}
- Spam Score Factors: {'✅ PASSED' if test_spam_score_factors() else '❌ FAILED'}

## Recommendations for Production

### DNS Configuration Required:
1. Add SPF record: `v=spf1 include:hostinger.com ~all`
2. Configure DKIM with Hostinger support
3. Add DMARC record: `v=DMARC1; p=quarantine; rua=mailto:<EMAIL>`

### Monitoring Setup:
1. Set up bounce rate monitoring (target: <5%)
2. Monitor spam complaint rates (target: <0.1%)
3. Regular blacklist checking
4. Deliverability testing with mail-tester.com

### Best Practices Implemented:
- ✅ Professional email templates with FxThrone branding
- ✅ Anti-spam headers and authentication
- ✅ Plain text versions for all HTML emails
- ✅ Proper SMTP SSL configuration
- ✅ Professional sender identification
- ✅ Compliance with CAN-SPAM requirements

## Next Steps:
1. Deploy email system to production
2. Configure DNS records as specified
3. Test with real email addresses
4. Monitor deliverability metrics
5. Implement feedback loops with major ISPs
"""
    
    # Save report to file
    with open("email_test_report.md", "w") as f:
        f.write(report)
    
    print("📄 Test report saved to 'email_test_report.md'")
    return report

def main():
    """Run all email system tests"""
    print("🚀 FxThrone Email System Comprehensive Testing")
    print("=" * 60)
    
    # Run all tests
    template_test = test_email_templates()
    sender_test = test_email_sender()
    spam_test = test_spam_score_factors()
    
    # Generate report
    generate_test_report()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 60)
    
    if template_test and sender_test and spam_test:
        print("🎉 ALL TESTS PASSED! Email system is ready for production.")
        print("\n📝 Next steps:")
        print("1. Configure DNS records (see EMAIL_DELIVERABILITY_GUIDE.md)")
        print("2. Test with real email addresses")
        print("3. Monitor deliverability metrics")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
