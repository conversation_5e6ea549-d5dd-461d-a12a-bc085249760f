from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import SQLModel, Field, Session, select
from typing import List, Optional
from datetime import datetime, timezone
from enum import Enum
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from db import get_session
from pydantic import BaseModel
from models.order import OrderModel, CompleteOrderModel, OrderTimeline, Stage2Account, LiveAccount

# Create account router
account_router = APIRouter(prefix="/account")

def create_html_email_template(title, content, button_text=None, button_url=None):
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {{
                font-family: 'Segoe UI', Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 0;
                background-color: #0a1a0a;
                color: #e6e6e6;
            }}
            .container {{
                max-width: 600px;
                margin: 20px auto;
                padding: 20px;
                background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%);
                border-radius: 5px;
                border: 1px solid #1e5f1e;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            }}
            .header {{
                text-align: center;
                padding: 20px 0;
                background: linear-gradient(145deg, #0a3a0a 0%, #155f15 100%);
                color: #ffffff;
                border-radius: 5px 5px 0 0;
                border-bottom: 1px solid #1e5f1e;
            }}
            .content {{
                padding: 20px;
                color: #e6e6e6;
            }}
            .button {{
                display: inline-block;
                padding: 10px 20px;
                background: linear-gradient(90deg, #006400, #00a000);
                color: #ffffff;
                text-decoration: none;
                border-radius: 5px;
                margin-top: 20px;
                font-weight: bold;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            .footer {{
                text-align: center;
                padding: 20px;
                color: #a3ffa3;
                font-size: 12px;
                background-color: #061306;
                border-top: 1px solid #1e5f1e;
                border-radius: 0 0 5px 5px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>{title}</h2>
            </div>
            <div class="content">
                {content}
                {f'<p style="text-align: center;"><a href="{button_url}" class="button">{button_text}</a></p>' if button_text and button_url else ''}
            </div>
            <div class="footer">
                <p>This is an automated message from FundedWhales. Please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    """
    return html_template

def send_email(to_email, subject, body, html_content=None):
    from_email = "<EMAIL>"
    from_password = "Fxthrone@123"

    msg = MIMEMultipart('related')
    msg["From"] = from_email
    msg["To"] = to_email
    msg["Subject"] = subject

    # Create alternative part for both plain text and HTML
    msgAlternative = MIMEMultipart('alternative')
    msg.attach(msgAlternative)

    # Create plain text version
    plain_text = body
    if "<html" in body.lower():
        # If body contains HTML, create a simple text version
        plain_text = "Please view this email in an HTML email client that supports HTML content."
    part1 = MIMEText(plain_text, "plain", "utf-8")
    msgAlternative.attach(part1)

    # Create HTML version
    if html_content:
        # Use provided HTML content
        part2 = MIMEText(html_content, "html", "utf-8")
        msgAlternative.attach(part2)
    elif "<html" in body.lower():
        # Body contains HTML
        part2 = MIMEText(body, "html", "utf-8")
        msgAlternative.attach(part2)
    else:
        # Convert plain text to HTML using the template
        html_content = create_html_email_template(
            title=subject,
            content=body.replace("\n", "<br>")
        )
        part2 = MIMEText(html_content, "html", "utf-8")
        msgAlternative.attach(part2)

    # Use Hostinger's SMTP server
    with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:
        server.login(from_email, from_password)
        server.sendmail(from_email, to_email, msg.as_string())

# Define account type enum
class AccountType(str, Enum):
    HFT = "hft"
    PHASE1 = "phase1"
    PHASE2 = "phase2"
    STAGE2 = "stage2"
    LIVE = "live"

# Define assignment type enum
class AssignmentType(str, Enum):
    COMPLETE = "complete"
    STAGE2 = "stage2"
    LIVE = "live"

# Enhanced Account Credentials Model
class AccountCredential(SQLModel, table=True):
    __tablename__ = "account_credentials"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    platform: str
    server: str
    platform_login: str
    platform_password: str
    account_size: str
    account_type: str = Field(default=AccountType.PHASE1)
    is_assigned: bool = Field(default=False)
    order_id: Optional[int] = Field(default=None)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: Optional[datetime] = Field(default=None)
    status: str = "pending"  # pending, active, completed, failed

    # Ensure SQLModel knows about this table
    @classmethod
    def create_table(cls, engine):
        if not engine.dialect.has_table(engine.connect(), cls.__tablename__):
            SQLModel.metadata.create_all(engine, tables=[cls.__table__])

# Request models
class AccountCredentialCreate(BaseModel):
    platform: str
    server: str
    platform_login: str
    platform_password: str
    account_size: str
    account_type: AccountType = AccountType.PHASE1

class AccountCredentialUpdate(BaseModel):
    platform: Optional[str] = None
    server: Optional[str] = None
    platform_login: Optional[str] = None
    platform_password: Optional[str] = None
    account_size: Optional[str] = None
    account_type: Optional[AccountType] = None
    is_assigned: Optional[bool] = None
    order_id: Optional[int] = None
    status: Optional[str] = None

class AccountCredentialResponse(BaseModel):
    id: int
    platform: str
    server: str
    platform_login: str
    platform_password: str
    account_size: str
    account_type: str
    is_assigned: bool
    order_id: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]
    status: str

class MultipleAccountCredentialsCreate(BaseModel):
    credentials: List[AccountCredentialCreate]

# Endpoints
@account_router.post("/credentials", response_model=List[AccountCredentialResponse])
async def create_account_credentials(
    credentials_data: MultipleAccountCredentialsCreate,
    session: Session = Depends(get_session)
):
    """
    Save multiple account credentials to the database
    """
    created_credentials = []

    for cred_data in credentials_data.credentials:
        # Check if credential with same login already exists
        existing_credential = session.exec(
            select(AccountCredential).where(
                AccountCredential.platform_login == cred_data.platform_login
            )
        ).first()

        if existing_credential:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Account with login {cred_data.platform_login} already exists"
            )

        # Create new credential
        new_credential = AccountCredential(
            platform=cred_data.platform,
            server=cred_data.server,
            platform_login=cred_data.platform_login,
            platform_password=cred_data.platform_password,
            account_size=cred_data.account_size,
            account_type=cred_data.account_type,
        )

        session.add(new_credential)
        created_credentials.append(new_credential)

    session.commit()

    # Refresh all credentials to get their IDs
    for credential in created_credentials:
        session.refresh(credential)

    return created_credentials

@account_router.get("/credentials", response_model=List[AccountCredentialResponse])
async def get_all_account_credentials(session: Session = Depends(get_session)):
    """
    Get all account credentials
    """
    credentials = session.exec(select(AccountCredential)).all()
    return credentials

@account_router.get("/credentials/pending", response_model=List[AccountCredentialResponse])
async def get_pending_account_credentials(session: Session = Depends(get_session)):
    """
    Get all pending account credentials (not assigned to any order)
    """
    credentials = session.exec(
        select(AccountCredential).where(
            AccountCredential.is_assigned == False
        )
    ).all()
    return credentials

@account_router.get("/credentials/{account_type}", response_model=List[AccountCredentialResponse])
async def get_account_credentials_by_type(
    account_type: AccountType,
    session: Session = Depends(get_session)
):
    """
    Get account credentials by type
    """
    credentials = session.exec(
        select(AccountCredential).where(
            AccountCredential.account_type == account_type
        )
    ).all()
    return credentials

@account_router.put("/credentials/{credential_id}", response_model=AccountCredentialResponse)
async def update_account_credential(
    credential_id: int,
    credential_data: AccountCredentialUpdate,
    session: Session = Depends(get_session)
):
    """
    Update an account credential
    """
    credential = session.get(AccountCredential, credential_id)
    if not credential:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account credential not found"
        )

    # Update fields if provided
    credential_data_dict = credential_data.model_dump(exclude_unset=True)
    for key, value in credential_data_dict.items():
        setattr(credential, key, value)

    # Update the updated_at timestamp
    credential.updated_at = datetime.now(timezone.utc)

    session.add(credential)
    session.commit()
    session.refresh(credential)

    return credential

@account_router.delete("/credentials/{credential_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_account_credential(
    credential_id: int,
    session: Session = Depends(get_session)
):
    """
    Delete an account credential
    """
    credential = session.get(AccountCredential, credential_id)
    if not credential:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account credential not found"
        )

    # Check if credential is assigned to an order
    if credential.is_assigned:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete an account that is assigned to an order"
        )

    session.delete(credential)
    session.commit()

    return None

@account_router.put("/credentials/{credential_id}/assign/{order_id}", response_model=AccountCredentialResponse)
async def assign_account_to_order(
    credential_id: int,
    order_id: int,
    assignment_type: AssignmentType = AssignmentType.COMPLETE,
    profit_target: Optional[float] = None,
    drawdown: Optional[float] = None,
    session: Session = Depends(get_session)
):
    """
    Assign an account to an order with options for complete, stage2, or live status

    Parameters:
    - credential_id: ID of the account credential to assign
    - order_id: ID of the order to assign the account to
    - assignment_type: Type of assignment (complete, stage2, or live)
    - profit_target: Optional profit target for the order
    - drawdown: Optional drawdown limit for the order
    """
    # Get the credential
    credential = session.get(AccountCredential, credential_id)
    if not credential:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account credential not found"
        )

    # Check if credential is already assigned
    if credential.is_assigned:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Account is already assigned to an order"
        )

    # Get the order
    order = session.get(OrderModel, order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

    # Only check for existing complete order when assignment type is COMPLETE
    if assignment_type == AssignmentType.COMPLETE:
        # Check if order is already completed
        existing_complete = session.exec(
            select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)
        ).first()
        if existing_complete:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Order has already been completed"
            )

    # Assign the account to the order
    credential.is_assigned = True
    credential.order_id = order_id
    credential.status = "active"
    credential.updated_at = datetime.now(timezone.utc)

    # Current timestamp for all records
    current_time = datetime.now(timezone.utc)

    # Handle different assignment types
    if assignment_type == AssignmentType.COMPLETE:
        # Create complete order record
        complete_order = CompleteOrderModel(
            order_id=order_id,
            server=credential.server,
            platform_login=credential.platform_login,
            platform_password=credential.platform_password,
            session_id=None,  # Optional field
            terminal_id=None,  # Optional field
            profit_target=profit_target,  # Use the provided profit_target
            completed_at=current_time
        )

        # Add timeline entry for order completion
        timeline_entry = OrderTimeline(
            order_id=order_id,
            event_type="completed",
            event_date=current_time,
            notes=f"Order completed with login {credential.platform_login}"
        )

        # Save records to database
        session.add(complete_order)
        session.add(timeline_entry)

    elif assignment_type == AssignmentType.STAGE2:
        # Check if a Stage2 account already exists
        existing_stage2 = session.exec(
            select(Stage2Account).where(Stage2Account.order_id == order_id)
        ).first()

        if existing_stage2:
            # Update the existing Stage2 account
            existing_stage2.server = credential.server
            existing_stage2.platform_login = credential.platform_login
            existing_stage2.platform_password = credential.platform_password
            if profit_target is not None:
                existing_stage2.profit_target = profit_target
            session.add(existing_stage2)
            stage2_account = existing_stage2
        else:
            # Create a new Stage2 account
            stage2_account = Stage2Account(
                order_id=order_id,
                server=credential.server,
                platform_login=credential.platform_login,
                platform_password=credential.platform_password,
                session_id=None,  # Optional field
                terminal_id=None,  # Optional field
                profit_target=profit_target,
                created_at=current_time,
                status="active"
            )

        # Store drawdown in notes if provided
        if drawdown is not None:
            # Add a note about drawdown to the timeline
            drawdown_note = OrderTimeline(
                order_id=order_id,
                event_type="drawdown_set",
                event_date=current_time,
                notes=f"Drawdown limit set to {drawdown}%"
            )
            session.add(drawdown_note)

        # Check if a complete order record already exists
        existing_complete = session.exec(
            select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)
        ).first()

        if existing_complete:
            # Update the existing record if needed
            if profit_target is not None and existing_complete.profit_target != profit_target:
                existing_complete.profit_target = profit_target
                session.add(existing_complete)
        else:
            # Create a new complete order record
            complete_order = CompleteOrderModel(
                order_id=order_id,
                server=credential.server,
                platform_login=credential.platform_login,
                platform_password=credential.platform_password,
                session_id=None,
                terminal_id=None,
                profit_target=profit_target,
                completed_at=current_time
            )
            session.add(complete_order)

        # Add timeline entry for stage2 creation
        timeline_entry = OrderTimeline(
            order_id=order_id,
            event_type="stage2_created",
            event_date=current_time,
            notes=f"Stage 2 account created with login {credential.platform_login}"
        )

        # Save records to database
        session.add(stage2_account)
        session.add(timeline_entry)

    elif assignment_type == AssignmentType.LIVE:
        # Check if a Live account already exists
        existing_live = session.exec(
            select(LiveAccount).where(LiveAccount.order_id == order_id)
        ).first()

        if existing_live:
            # Update the existing Live account
            existing_live.server = credential.server
            existing_live.platform_login = credential.platform_login
            existing_live.platform_password = credential.platform_password
            session.add(existing_live)
            live_account = existing_live
        else:
            # Create a new Live account
            live_account = LiveAccount(
                order_id=order_id,
                server=credential.server,
                platform_login=credential.platform_login,
                platform_password=credential.platform_password,
                session_id=None,  # Optional field
                terminal_id=None,  # Optional field
                created_at=current_time,
                profit_share=80.0,  # Default profit share percentage
                status="active"
            )

        # Check if a complete order record already exists
        existing_complete = session.exec(
            select(CompleteOrderModel).where(CompleteOrderModel.order_id == order_id)
        ).first()

        if existing_complete:
            # Update the existing record if needed
            if profit_target is not None and existing_complete.profit_target != profit_target:
                existing_complete.profit_target = profit_target
                session.add(existing_complete)
        else:
            # Create a new complete order record
            complete_order = CompleteOrderModel(
                order_id=order_id,
                server=credential.server,
                platform_login=credential.platform_login,
                platform_password=credential.platform_password,
                session_id=None,
                terminal_id=None,
                profit_target=profit_target,
                completed_at=current_time
            )
            session.add(complete_order)

        # Add timeline entry for live account creation
        timeline_entry = OrderTimeline(
            order_id=order_id,
            event_type="live_created",
            event_date=current_time,
            notes=f"Live account created with login {credential.platform_login}"
        )

        # Save records to database
        session.add(live_account)
        session.add(timeline_entry)

    # Save credential changes to database
    session.add(credential)
    session.commit()
    session.refresh(credential)

    # Send email notification
    try:
        # Set subject based on assignment type
        if assignment_type == AssignmentType.COMPLETE:
            subject = "Order Completed Successfully"
        elif assignment_type == AssignmentType.STAGE2:
            subject = "Stage 2 Account Created Successfully"
        elif assignment_type == AssignmentType.LIVE:
            subject = "Live Account Created Successfully"
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Your Trading Account Details</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Arial, sans-serif; background-color: #0a1a0a; color: #e6e6e6;">
            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
                <td style="padding: 0;">
                <table role="presentation" cellpadding="0" cellspacing="0" width="650" style="border-collapse: collapse; margin: 0 auto; background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%); border: 1px solid #1e5f1e;">

                    <!-- Header with Logo -->
                    <tr>
                    <td style="padding: 25px 30px 15px; text-align: center;">
                        <div style="display: inline-block; background: linear-gradient(145deg, #0a3a0a 0%, #155f15 100%); padding: 20px; border-radius: 15px; box-shadow: 0 0 25px rgba(0, 130, 0, 0.3);">
                        <h1 style="color: #ffffff; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FX<span style="color: #00c800;">ENTRA</span></h1>
                        <p style="color: #a3ffa3; margin: 5px 0 0; font-size: 14px; letter-spacing: 3px; text-transform: uppercase;">PROP TRADING</p>
                        </div>
                    </td>
                    </tr>

                    <!-- Order Completion Banner -->
                    <tr>
                    <td style="padding: 0; position: relative;">
                        <div style="background-color: #061306; height: 120px; position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(145deg, rgba(10, 64, 10, 0.85) 0%, rgba(21, 106, 21, 0.85) 100%); display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
                            <div style="background: rgba(6, 37, 6, 0.7); padding: 20px 30px; border-radius: 10px; border: 1px solid rgba(0, 130, 0, 0.3);">
                                <h2 style="color: #ffffff; margin: 0; font-size: 24px; letter-spacing: 1px; text-transform: uppercase; font-weight: 700;">
                                    {
                                        "ACCOUNT DETAILS" if assignment_type == AssignmentType.COMPLETE else
                                        "STAGE 2 ACCOUNT DETAILS" if assignment_type == AssignmentType.STAGE2 else
                                        "LIVE ACCOUNT DETAILS"
                                    }
                                </h2>
                                <p style="color: #a3ffa3; margin: 10px 0 0; font-size: 16px; letter-spacing: 1px;">ORDER #: FxE{order_id}</p>
                            </div>
                        </div>
                        </div>
                    </td>
                    </tr>

                    <!-- Main Content -->
                    <tr>
                    <td style="padding: 30px 30px 20px;">
                        <h3 style="color: #00c800; margin: 0 0 15px; font-size: 22px; letter-spacing: 1px;">Dear {order.username},</h3>

                        <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">
                            {
                                "Your trading account has been successfully set up." if assignment_type == AssignmentType.COMPLETE else
                                "Your Stage 2 account has been successfully created." if assignment_type == AssignmentType.STAGE2 else
                                "Congratulations! Your Live account has been successfully created."
                            }
                            Below are your account credentials:
                        </p>

                        <!-- Account Details Box -->
                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0; background: linear-gradient(145deg, #0a2a0a 0%, #0d3d0d 100%); border-radius: 10px; border: 1px solid #1e5f1e;">
                        <tr>
                            <td style="padding: 0;">
                            <!-- Header -->
                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                <tr>
                                <td style="background: linear-gradient(90deg, #006400, #00a000); padding: 12px 20px; border-radius: 10px 10px 0 0;">
                                    <h4 style="color: #ffffff; margin: 0; font-size: 18px; text-transform: uppercase; letter-spacing: 1px;">
                                        {
                                            "Account Credentials" if assignment_type == AssignmentType.COMPLETE else
                                            "Stage 2 Account Credentials" if assignment_type == AssignmentType.STAGE2 else
                                            "Live Account Credentials"
                                        }
                                    </h4>
                                </td>
                                </tr>
                            </table>

                            <!-- Content -->
                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                <tr>
                                <td style="padding: 20px;">
                                    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                            <tr>
                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Server:</td>
                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{credential.server}</td>
                                            </tr>
                                        </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                            <tr>
                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Login:</td>
                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{credential.platform_login}</td>
                                            </tr>
                                        </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                            <tr>
                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Password:</td>
                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{credential.platform_password}</td>
                                            </tr>
                                        </table>
                                        </td>
                                    </tr>
                                    {f'''<tr>
                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                            <tr>
                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Profit Target:</td>
                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{profit_target}</td>
                                            </tr>
                                        </table>
                                        </td>
                                    </tr>''' if profit_target is not None else ''}

                                    {f'''<tr>
                                        <td style="padding: 10px 0; border-bottom: 1px solid rgba(30, 95, 30, 0.5);">
                                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                            <tr>
                                                <td width="40%" style="color: #a3ffa3; font-size: 16px;">Drawdown Limit:</td>
                                                <td width="60%" style="color: #ffffff; font-size: 16px;">{drawdown}%</td>
                                            </tr>
                                        </table>
                                        </td>
                                    </tr>''' if assignment_type == AssignmentType.STAGE2 and drawdown is not None else ''}
                                    </table>
                                </td>
                                </tr>
                            </table>
                            </td>
                        </tr>
                        </table>

                        <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">You can now start trading with your account. Good luck with your trading journey!</p>

                        <!-- View Details CTA -->
                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0;">
                        <tr>
                            <td style="text-align: center;">
                                <a href="https://fundedwhales.com/order/{order_id}" style="display: inline-block; background: linear-gradient(90deg, #006400, #00a000); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 5px; font-weight: bold; font-size: 16px; text-transform: uppercase; letter-spacing: 1px;">VIEW ORDER DETAILS</a>
                            </td>
                        </tr>
                        </table>

                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin-top: 25px;">
                        <tr>
                            <td style="padding: 0;">
                                <p style="color: #e6e6e6; margin: 0; font-size: 16px; line-height: 1.6;">Best Regards,</p>
                            <p style="color: #00c800; margin: 10px 0 0; font-size: 18px; font-weight: bold;">The FXentra Team</p>
                            </td>
                        </tr>
                        </table>
                    </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                    <td style="background-color: #061306; padding: 20px; border-top: 1px solid #1e5f1e;">
                        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                        <tr>
                            <td style="text-align: center; color: #a3ffa3; font-size: 14px;">
                            <p style="margin: 0 0 10px;">© 2025 FundedWhales Prop Trading. All rights reserved.</p>
                            </td>
                        </tr>
                        </table>
                    </td>
                    </tr>
                </table>
                </td>
            </tr>
            </table>
        </body>
        </html>
        """

        send_email(order.email, subject, body="Your trading account has been successfully set up.", html_content=html_content)
    except Exception as e:
        # Log the error but don't fail the operation
        print(f"Error sending email: {str(e)}")

    return credential

@account_router.put("/credentials/{credential_id}/complete", response_model=AccountCredentialResponse)
async def complete_account(
    credential_id: int,
    session: Session = Depends(get_session)
):
    """
    Mark an account as completed
    """
    credential = session.get(AccountCredential, credential_id)
    if not credential:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account credential not found"
        )

    # Update the account status
    credential.status = "completed"
    credential.updated_at = datetime.now(timezone.utc)

    session.add(credential)
    session.commit()
    session.refresh(credential)

    return credential

@account_router.put("/credentials/{credential_id}/promote", response_model=AccountCredentialResponse)
async def promote_account(
    credential_id: int,
    new_type: AccountType,
    session: Session = Depends(get_session)
):
    """
    Promote an account to a new type (e.g., from Phase1 to Phase2 or Stage2 to Live)
    """
    credential = session.get(AccountCredential, credential_id)
    if not credential:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account credential not found"
        )

    # Update the account type
    credential.account_type = new_type
    credential.updated_at = datetime.now(timezone.utc)

    session.add(credential)
    session.commit()
    session.refresh(credential)

    return credential