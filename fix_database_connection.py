#!/usr/bin/env python3
"""
Database connection fix script.
Provides multiple solutions for the DNS resolution issue.
"""

import os
import sys
import socket
import time
from urllib.parse import urlparse

def test_dns_with_different_servers():
    """Test DNS resolution with different DNS servers"""
    print("🔍 Testing DNS Resolution with Different Servers...")
    
    hostname = "ep-super-shadow-aeu6oyxe-pooler.c-2.us-east-2.aws.neon.tech"
    
    # Try default DNS
    try:
        ip = socket.gethostbyname(hostname)
        print(f"   ✅ Default DNS resolved: {ip}")
        return True
    except socket.gaierror:
        print("   ❌ Default DNS failed")
    
    print("   💡 Try changing your DNS servers to:")
    print("      - Google DNS: *******, *******")
    print("      - Cloudflare DNS: *******, *******")
    print("      - OpenDNS: **************, **************")
    
    return False

def create_alternative_connection_methods():
    """Create alternative connection methods"""
    print("\n🔧 Creating Alternative Connection Methods...")
    
    # Method 1: Connection with retry logic
    connection_with_retry = '''
import time
import psycopg2
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool

def create_engine_with_retry(database_url, max_retries=3, retry_delay=5):
    """Create database engine with retry logic"""
    for attempt in range(max_retries):
        try:
            # Add connection pooling and timeout settings
            engine = create_engine(
                database_url,
                poolclass=NullPool,  # Disable connection pooling
                connect_args={
                    "connect_timeout": 30,
                    "application_name": "FundedWhales_App"
                },
                echo=False
            )
            
            # Test the connection
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            
            print(f"✅ Database connection successful on attempt {attempt + 1}")
            return engine
            
        except Exception as e:
            print(f"❌ Connection attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                print(f"⏳ Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                print("❌ All connection attempts failed")
                raise e

# Usage in your db.py:
# engine = create_engine_with_retry(DATABASE_URL)
'''
    
    with open("connection_with_retry.py", "w") as f:
        f.write(connection_with_retry)
    
    print("   ✅ Created connection_with_retry.py")
    
    # Method 2: Alternative database configuration
    alt_db_config = '''
import os
from sqlalchemy import create_engine
from sqlmodel import Session

# Alternative database configuration with better error handling
def get_database_engine():
    """Get database engine with comprehensive error handling"""
    database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        raise ValueError("DATABASE_URL environment variable not set")
    
    # Ensure proper SSL mode for Neon
    if "sslmode=" not in database_url:
        database_url += "?sslmode=require"
    
    # Convert postgres:// to postgresql:// if needed
    if database_url.startswith("postgres://"):
        database_url = database_url.replace("postgres://", "postgresql://", 1)
    
    try:
        engine = create_engine(
            database_url,
            echo=False,
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=3600,   # Recycle connections every hour
            connect_args={
                "connect_timeout": 30,
                "application_name": "FundedWhales",
                "sslmode": "require"
            }
        )
        
        # Test connection
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        
        return engine
        
    except Exception as e:
        print(f"Database connection error: {e}")
        print("Please check:")
        print("1. Internet connection")
        print("2. DATABASE_URL is correct")
        print("3. Database server is accessible")
        raise e

# Usage:
# engine = get_database_engine()
'''
    
    with open("alternative_db_config.py", "w") as f:
        f.write(alt_db_config)
    
    print("   ✅ Created alternative_db_config.py")

def create_network_diagnostic_script():
    """Create a network diagnostic script"""
    print("\n🌐 Creating Network Diagnostic Script...")
    
    diagnostic_script = '''
import socket
import subprocess
import platform

def diagnose_network_connectivity():
    """Comprehensive network connectivity diagnosis"""
    print("🔍 Network Connectivity Diagnosis")
    print("=" * 40)
    
    hostname = "ep-super-shadow-aeu6oyxe-pooler.c-2.us-east-2.aws.neon.tech"
    port = 5432
    
    # Test 1: DNS Resolution
    print("\\n1. Testing DNS Resolution...")
    try:
        ip = socket.gethostbyname(hostname)
        print(f"   ✅ Resolved to: {ip}")
    except socket.gaierror as e:
        print(f"   ❌ DNS Resolution failed: {e}")
        return False
    
    # Test 2: TCP Connection
    print("\\n2. Testing TCP Connection...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ TCP connection successful")
        else:
            print(f"   ❌ TCP connection failed (error code: {result})")
            return False
    except Exception as e:
        print(f"   ❌ TCP connection error: {e}")
        return False
    
    # Test 3: Ping test (if available)
    print("\\n3. Testing Ping...")
    try:
        if platform.system().lower() == "windows":
            result = subprocess.run(["ping", "-n", "4", hostname], 
                                  capture_output=True, text=True, timeout=30)
        else:
            result = subprocess.run(["ping", "-c", "4", hostname], 
                                  capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("   ✅ Ping successful")
        else:
            print("   ⚠️  Ping failed (but this might be normal for database servers)")
    except Exception as e:
        print(f"   ⚠️  Ping test error: {e}")
    
    print("\\n✅ Network connectivity appears to be working!")
    return True

if __name__ == "__main__":
    diagnose_network_connectivity()
'''
    
    with open("network_diagnostic.py", "w") as f:
        f.write(diagnostic_script)
    
    print("   ✅ Created network_diagnostic.py")

def provide_immediate_solutions():
    """Provide immediate solutions"""
    print("\n💡 Immediate Solutions to Try:")
    print("=" * 40)
    
    print("1. 🔄 Restart your application:")
    print("   - Close all terminals/processes")
    print("   - Restart your development server")
    
    print("\n2. 🌐 Check your internet connection:")
    print("   - Try accessing other websites")
    print("   - Test: ping google.com")
    
    print("\n3. 🔥 Check Windows Firewall/Antivirus:")
    print("   - Temporarily disable firewall")
    print("   - Add Python to firewall exceptions")
    
    print("\n4. 📡 Change DNS servers:")
    print("   - Windows: Network Settings > Change adapter options")
    print("   - Set DNS to ******* and *******")
    
    print("\n5. 🔄 Flush DNS cache:")
    print("   - Run: ipconfig /flushdns")
    
    print("\n6. 🌍 Try from different network:")
    print("   - Use mobile hotspot")
    print("   - Try from different location")
    
    print("\n7. ⏰ Wait and retry:")
    print("   - Neon database might be temporarily unavailable")
    print("   - Check Neon status page")

def main():
    """Main function"""
    print("🚀 Database Connection Fix Script")
    print("=" * 50)
    
    # Test current DNS resolution
    test_dns_with_different_servers()
    
    # Create alternative connection methods
    create_alternative_connection_methods()
    
    # Create diagnostic tools
    create_network_diagnostic_script()
    
    # Provide immediate solutions
    provide_immediate_solutions()
    
    print("\n" + "=" * 50)
    print("📋 Files Created:")
    print("   ✅ connection_with_retry.py - Retry logic for connections")
    print("   ✅ alternative_db_config.py - Better database configuration")
    print("   ✅ network_diagnostic.py - Network connectivity testing")
    
    print("\n🔧 Next Steps:")
    print("   1. Run: python network_diagnostic.py")
    print("   2. Try the immediate solutions above")
    print("   3. Update your db.py with retry logic")
    print("   4. Contact Neon support if issue persists")

if __name__ == "__main__":
    main()
