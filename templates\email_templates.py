from datetime import datetime

def create_base_email_template(title, content, button_text=None, button_url=None, accent_color="#007BFF"):
    """
    Professional email template with FxThrone branding and anti-spam optimizations
    """
    # Convert SVG logo to base64 for embedding
    logo_svg = """<svg width="120" height="40" viewBox="0 0 661.77 599.03" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <style>
                .cls-1 { fill: none; stroke: #000; stroke-miterlimit: 10; stroke-width: 2px; }
                .cls-2 { fill: #172554; }
                .cls-3 { fill: #fd6900; }
            </style>
        </defs>
        <g>
            <path class="cls-2" d="m661.77,299.1c-17.12,8.76-33.88,18.13-50.25,28.1h-215.33v191.55c-19.77,25.3-38.04,51.83-54.67,79.46V0c16.63,27.62,34.9,54.16,54.67,79.46,45.44,58.21,98.82,109.95,158.47,153.58h-84.24c-26.29-22.4-51.09-46.5-74.23-72.12v113.12h220.39c14.76,8.84,29.83,17.19,45.2,25.06Z"/>
            <path class="cls-1" d="m607.3,327.19h4.22c-.65.39-1.32.79-1.97,1.21-.76-.39-1.5-.8-2.25-1.21h0Z"/>
            <path class="cls-3" d="m315.7,7.46v591.58c-16.67-27.03-34.94-61.29-54.67-86.06v-185.78h-88.08v91.71c-18-16.36-36.75-31.91-56.19-46.58v-146.46c19.44-14.67,38.19-30.22,56.19-46.58v94.75h88.08V85.23c13.94-17.49,27.14-35.58,39.56-54.23,5.19-7.75,10.24-15.6,15.11-23.54Z"/>
            <path class="cls-3" d="m90.94,244.56v109.07c-13.27-9.22-26.85-18.04-40.68-26.44-16.37-9.96-33.14-19.33-50.25-28.1,15.37-7.87,30.43-16.22,45.2-25.06,15.6-9.31,30.86-19.14,45.74-29.48h0Z"/>
        </g>
    </svg>"""

    html_template = f"""<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml" lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="x-apple-disable-message-reformatting">
        <title>{title} - FxThrone</title>
        <!--[if mso]>
        <noscript>
            <xml>
                <o:OfficeDocumentSettings>
                    <o:PixelsPerInch>96</o:PixelsPerInch>
                </o:OfficeDocumentSettings>
            </xml>
        </noscript>
        <![endif]-->
        <style type="text/css">
            /* Reset styles */
            body, table, td, p, a, li, blockquote {{ -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }}
            table, td {{ border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; }}
            img {{ -ms-interpolation-mode: bicubic; }}

            /* Base styles */
            body {{ margin: 0; padding: 0; width: 100% !important; min-width: 100%; font-family: Arial, Helvetica, sans-serif; font-size: 16px; line-height: 1.6; color: #333333; background-color: #f8f9fa; }}
            .email-container {{ max-width: 600px; margin: 0 auto; background: #FFFFFF; }}
            .email-header {{ background: #ffffff; padding: 30px 20px; text-align: center; border-bottom: 2px solid #e9ecef; }}
            .email-content {{ padding: 30px 20px; background: #ffffff; }}
            .email-footer {{ background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 14px; }}
            .button {{ display: inline-block; padding: 14px 28px; background-color: {accent_color}; color: #FFFFFF !important; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; text-align: center; }}
            .button:hover {{ background-color: #0056b3; }}
            .logo {{ max-width: 120px; height: auto; }}
            .logo-container {{ display: flex; align-items: center; justify-content: center; gap: 20px; margin-bottom: 15px; }}
            .logo-item {{ flex-shrink: 0; }}

            /* Responsive styles */
            @media only screen and (max-width: 600px) {{
                .email-container {{ width: 100% !important; }}
                .email-header, .email-content, .email-footer {{ padding: 20px 15px !important; }}
                .button {{ padding: 12px 24px !important; font-size: 14px !important; }}
                .logo {{ max-width: 100px !important; }}
                .logo-container {{ flex-direction: column !important; gap: 15px !important; }}
                .logo-item img {{ max-width: 100px !important; max-height: 35px !important; }}
            }}
        </style>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8f9fa;">
        <!-- Email Wrapper -->
        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; background-color: #f8f9fa; min-height: 100vh;">
            <tr>
                <td align="center" style="padding: 20px 10px;">
                    <!-- Main Email Container -->
                    <table class="email-container" role="presentation" cellpadding="0" cellspacing="0" width="600" style="margin: 0 auto; border-collapse: collapse; background: #FFFFFF; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">

                        <!-- Header with Logos -->
                        <tr>
                            <td class="email-header" style="background: #ffffff; padding: 30px 20px; text-align: center; border-bottom: 2px solid #e9ecef;">
                                <!-- Logos Container -->
                                <div class="logo-container" style="margin-bottom: 15px; display: flex; align-items: center; justify-content: center; gap: 20px;">
                                    <!-- FxThrone SVG Logo -->
                                    <div class="logo-item" style="flex-shrink: 0;">
                                        {logo_svg}
                                    </div>
                                    <!-- Additional Logo from Cloudinary -->
                                    <div class="logo-item" style="flex-shrink: 0;">
                                        <img src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
                                             alt="FxThrone Partner Logo"
                                             class="logo"
                                             style="max-width: 120px; height: auto; max-height: 40px; display: block;"
                                             width="120"
                                             height="40">
                                    </div>
                                </div>

                                <!-- Fallback for email clients that don't support flexbox -->
                                <!--[if mso]>
                                <table role="presentation" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
                                    <tr>
                                        <td style="padding-right: 10px;">
                                            {logo_svg}
                                        </td>
                                        <td style="padding-left: 10px;">
                                            <img src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
                                                 alt="FxThrone Partner Logo"
                                                 style="max-width: 120px; height: auto; max-height: 40px;"
                                                 width="120"
                                                 height="40">
                                        </td>
                                    </tr>
                                </table>
                                <![endif]-->

                                <!-- Company Name and Tagline -->
                                <h1 style="color: #172554; margin: 15px 0 5px 0; font-size: 24px; font-weight: 700; letter-spacing: 1px;">FxThrone</h1>
                                <p style="color: #6c757d; margin: 0; font-size: 12px; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;">Professional Prop Trading</p>
                            </td>
                        </tr>

                        <!-- Title Section -->
                        <tr>
                            <td style="padding: 20px 20px 10px 20px; text-align: center;">
                                <h2 style="color: #172554; margin: 0; font-size: 22px; font-weight: 600; line-height: 1.3;">{title}</h2>
                            </td>
                        </tr>

                        <!-- Main Content -->
                        <tr>
                            <td class="email-content" style="padding: 20px; color: #333333; font-size: 16px; line-height: 1.6; background: #ffffff;">
                                {content}
                            </td>
                        </tr>

                        <!-- Call-to-Action Button -->
                        {"" if not button_text else f'''
                        <tr>
                            <td style="padding: 20px; text-align: center; background: #ffffff;">
                                <table role="presentation" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
                                    <tr>
                                        <td style="border-radius: 6px; background-color: {accent_color};">
                                            <a href="{button_url}" class="button" style="display: inline-block; padding: 14px 28px; background-color: {accent_color}; color: #FFFFFF !important; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px; text-align: center;">{button_text}</a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        '''}

                        <!-- Footer -->
                        <tr>
                            <td class="email-footer" style="background: #f8f9fa; padding: 25px 20px; text-align: center; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 14px;">
                                <!-- Company Info -->
                                <div style="margin-bottom: 20px;">
                                    <h3 style="color: #172554; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">FxThrone</h3>
                                    <p style="margin: 0 0 5px 0; font-size: 12px; color: #6c757d;">Professional Prop Trading Platform</p>
                                    <p style="margin: 0; font-size: 12px; color: #6c757d;">Empowering Traders Worldwide</p>
                                </div>

                                <!-- Contact Information -->
                                <div style="margin-bottom: 20px; padding: 15px; background: #ffffff; border-radius: 6px; border: 1px solid #e9ecef;">
                                    <p style="margin: 0 0 8px 0; font-size: 13px; color: #333333; font-weight: 600;">Contact Support</p>
                                    <p style="margin: 0 0 5px 0; font-size: 12px; color: #6c757d;">Email: <a href="mailto:<EMAIL>" style="color: {accent_color}; text-decoration: none;"><EMAIL></a></p>
                                    <p style="margin: 0; font-size: 12px; color: #6c757d;">Website: <a href="https://fxthrone.com" style="color: {accent_color}; text-decoration: none;">www.fxthrone.com</a></p>
                                </div>

                                <!-- Legal Footer -->
                                <div style="border-top: 1px solid #e9ecef; padding-top: 15px;">
                                    <p style="margin: 0 0 8px 0; font-size: 11px; color: #6c757d;">© {datetime.now().year} FxThrone Prop Trading. All rights reserved.</p>
                                    <p style="margin: 0 0 8px 0; font-size: 11px; color: #6c757d;">This email was sent to you because you have an account with FxThrone.</p>
                                    <p style="margin: 0; font-size: 10px; color: #adb5bd;">If you no longer wish to receive these emails, please contact our support team.</p>
                                </div>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """
    return html_template

def create_welcome_email(username, referral_code=None):
    """
    Modern welcome email template with referral information
    """
    referral_section = ""
    if referral_code:
        referral_section = f"""
        <div style="background: #f8f9fa; border: 1px solid #007BFF; border-radius: 4px; padding: 20px; margin: 20px 0; text-align: center;">
            <h3 style="color: #007BFF; margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">Your Referral Code</h3>
            <div style="background: #FFFFFF; border: 1px solid #007BFF; border-radius: 4px; padding: 10px; margin: 10px 0;">
                <code style="font-size: 20px; font-weight: 700; color: #333333;">{referral_code}</code>
            </div>
            <p style="margin: 10px 0 0 0; color: #333333; font-size: 14px;">Share this code and earn <strong>50 points</strong> for each successful referral!</p>
        </div>
        """

    content = f"""
    <div style="margin-bottom: 25px;">
        <p style="margin: 0 0 5px 0; color: #6c757d; font-size: 14px;">Dear {username},</p>
        <h3 style="color: #172554; margin: 0; font-size: 20px; font-weight: 600;">Welcome to FxThrone</h3>
    </div>

    <p style="margin: 0 0 20px; line-height: 1.6;">Thank you for joining FxThrone, the premier proprietary trading platform. We are committed to providing you with the tools, resources, and funding opportunities to excel in your trading career.</p>

    {referral_section}

    <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #172554; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">Getting Started</h4>
        <p style="margin: 0 0 15px 0; color: #333333; font-size: 14px;">To begin your journey with FxThrone, please complete the following steps:</p>

        <div style="margin: 15px 0;">
            <div style="padding: 12px; background: #ffffff; border-left: 3px solid #007BFF; margin-bottom: 12px;">
                <h5 style="margin: 0 0 5px 0; color: #172554; font-size: 14px; font-weight: 600;">Step 1: Complete Your Profile</h5>
                <p style="margin: 0; color: #6c757d; font-size: 13px;">Provide your trading experience and risk management preferences to help us tailor your experience.</p>
            </div>
            <div style="padding: 12px; background: #ffffff; border-left: 3px solid #007BFF; margin-bottom: 12px;">
                <h5 style="margin: 0 0 5px 0; color: #172554; font-size: 14px; font-weight: 600;">Step 2: Select Your Challenge</h5>
                <p style="margin: 0; color: #6c757d; font-size: 13px;">Choose from our range of funding options, from $10,000 to $200,000, based on your trading goals.</p>
            </div>
            <div style="padding: 12px; background: #ffffff; border-left: 3px solid #007BFF;">
                <h5 style="margin: 0 0 5px 0; color: #172554; font-size: 14px; font-weight: 600;">Step 3: Begin Trading</h5>
                <p style="margin: 0; color: #6c757d; font-size: 13px;">Start your evaluation period and demonstrate your trading skills to qualify for funding.</p>
            </div>
        </div>
    </div>

    <div style="background: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #172554; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">Important Information</h4>
        <ul style="margin: 0; padding-left: 20px; color: #333333; font-size: 14px; line-height: 1.6;">
            <li style="margin-bottom: 8px;">All trading activities are subject to our Terms of Service and Risk Management Rules</li>
            <li style="margin-bottom: 8px;">Our support team is available to assist you throughout your trading journey</li>
            <li style="margin-bottom: 0;">Please ensure you understand all risk factors before beginning live trading</li>
        </ul>
    </div>

    <p style="margin: 20px 0; color: #333333; font-size: 14px; line-height: 1.6;">We look forward to supporting your success as a professional trader. Should you have any questions or require assistance, please do not hesitate to contact our support team.</p>

    <div style="margin: 25px 0; padding: 15px 0; border-top: 1px solid #e9ecef;">
        <p style="margin: 0; color: #333333; font-size: 14px; font-weight: 600;">Best regards,</p>
        <p style="margin: 5px 0 0 0; color: #6c757d; font-size: 14px;">The FxThrone Team</p>
    </div>
    """
    return create_base_email_template(
        "Welcome to FxThrone!",
        content,
        "Access Your Dashboard",
        "https://fxthrone.com/dashboard",
        "#007BFF"
    )

def create_referral_bonus_email(username, referred_username, referred_email, points_earned=50):
    """
    Referral bonus notification email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #38A169 0%, #2F855A 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">🎉</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Congratulations, {username}!</h3>
    </div>

    <p style="margin: 0 0 25px; font-size: 18px; color: #2D3748; text-align: center;">You've earned <strong style="color: #38A169;">{points_earned} points</strong> for a successful referral!</p>

    <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 2px solid #38A169; border-radius: 16px; padding: 30px; margin: 30px 0; text-align: center; position: relative;">
        <div style="position: absolute; top: -10px; right: 20px; background: #38A169; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: 600;">NEW REFERRAL</div>

        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
            <div style="background: #FFFFFF; border: 2px solid #38A169; border-radius: 50%; width: 60px; height: 60px; line-height: 60px; text-align: center; margin-right: 15px; font-size: 24px;">👤</div>
            <div style="text-align: left;">
                <h4 style="color: #1A365D; margin: 0; font-size: 18px; font-weight: 600;">{referred_username}</h4>
                <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">{referred_email}</p>
            </div>
        </div>

        <div style="background: #FFFFFF; border: 2px dashed #38A169; border-radius: 12px; padding: 20px; margin: 20px 0;">
            <div style="display: flex; align-items: center; justify-content: center;">
                <div style="background: #38A169; color: white; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; text-align: center; margin-right: 15px; font-size: 20px;">+</div>
                <div>
                    <h3 style="color: #38A169; margin: 0; font-size: 28px; font-weight: 800;">{points_earned} Points</h3>
                    <p style="margin: 0; color: #4A5568; font-size: 14px;">Added to your account</p>
                </div>
            </div>
        </div>
    </div>

    <div style="background: #F8FAFC; border-radius: 12px; padding: 25px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 15px 0; font-size: 18px; font-weight: 600; text-align: center;">💡 Keep Earning More!</h4>
        <p style="margin: 0 0 15px 0; color: #4A5568; text-align: center;">Share your referral code with more friends and earn 50 points for each successful signup!</p>
        <div style="text-align: center;">
            <div style="background: #FFFFFF; border: 2px solid #FF6B35; border-radius: 8px; padding: 15px; display: inline-block; margin: 10px 0;">
                <p style="margin: 0 0 5px 0; color: #4A5568; font-size: 12px; font-weight: 600;">YOUR REFERRAL CODE</p>
                <code style="font-size: 20px; font-weight: 800; color: #FF6B35; letter-spacing: 2px;">SHARE_ME</code>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Thank you for helping FxThrone grow!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Happy Trading! 🚀</p>
    </div>
    """
    return create_base_email_template(
        "You Earned Referral Points! 🎁",
        content,
        "View Your Points",
        "https://fxthrone.com/dashboard/points",
        "#38A169"
    )

def create_points_milestone_email(username, total_points, milestone):
    """
    Points milestone achievement email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #805AD5 0%, #6B46C1 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">🏆</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Milestone Achieved!</h3>
    </div>

    <p style="margin: 0 0 25px; font-size: 18px; color: #2D3748; text-align: center;">Congratulations <strong>{username}</strong>! You've reached <strong style="color: #805AD5;">{milestone} points</strong>!</p>

    <div style="background: linear-gradient(135deg, #FAF5FF 0%, #E9D8FD 100%); border: 2px solid #805AD5; border-radius: 16px; padding: 30px; margin: 30px 0; text-align: center;">
        <div style="background: #FFFFFF; border: 2px solid #805AD5; border-radius: 12px; padding: 25px; margin: 20px 0;">
            <h2 style="color: #805AD5; margin: 0; font-size: 36px; font-weight: 800;">{total_points}</h2>
            <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 16px; font-weight: 600;">Total Points Earned</p>
        </div>

        <div style="margin: 20px 0;">
            <p style="margin: 0; color: #1A365D; font-size: 16px; font-weight: 600;">🎯 Next Milestone: {milestone + 100} Points</p>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Keep earning points through referrals and activities!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">You're doing amazing! 🌟</p>
    </div>
    """
    return create_base_email_template(
        f"🏆 {milestone} Points Milestone Reached!",
        content,
        "View Your Dashboard",
        "https://fxthrone.com/dashboard/points",
        "#805AD5"
    )

def create_giveaway_entry_email(username, giveaway_title, prize_description, account_size, end_date):
    """
    Giveaway entry notification email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #F6AD55 0%, #ED8936 100%); color: white; border-radius: 50%; width: 100px; height: 100px; line-height: 100px; margin-bottom: 20px; font-size: 50px; box-shadow: 0 10px 30px rgba(246, 173, 85, 0.3);">🎁</div>
        <h2 style="color: #1A365D; margin: 0; font-size: 32px; font-weight: 800;">YOU'RE IN THE GIVEAWAY!</h2>
        <p style="margin: 10px 0 0 0; color: #F6AD55; font-size: 18px; font-weight: 600;">Congratulations on Your Entry!</p>
    </div>

    <p style="margin: 0 0 30px; font-size: 18px; color: #2D3748; text-align: center;">Amazing news <strong>{username}</strong>! Your recent <strong style="color: #F6AD55;">${account_size:,.0f}</strong> account purchase has automatically entered you into our exclusive giveaway!</p>

    <!-- Giveaway Details -->
    <div style="background: linear-gradient(135deg, #FFFBF0 0%, #FEF5E7 100%); border: 3px solid #F6AD55; border-radius: 20px; padding: 35px; margin: 30px 0; text-align: center; position: relative; overflow: hidden;">
        <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(246, 173, 85, 0.1); border-radius: 50%;"></div>
        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(246, 173, 85, 0.1); border-radius: 50%;"></div>

        <div style="position: relative; z-index: 2;">
            <h3 style="color: #1A365D; margin: 0 0 15px 0; font-size: 24px; font-weight: 700;">{giveaway_title}</h3>

            <div style="background: #FFFFFF; border: 3px dashed #F6AD55; border-radius: 16px; padding: 25px; margin: 25px 0;">
                <div style="display: inline-block; background: #F6AD55; color: white; border-radius: 50%; width: 60px; height: 60px; line-height: 60px; margin-bottom: 15px; font-size: 30px;">💰</div>
                <h2 style="color: #1A365D; margin: 0; font-size: 28px; font-weight: 800;">{prize_description}</h2>
                <p style="margin: 10px 0 0 0; color: #4A5568; font-size: 16px;">The ultimate trading opportunity!</p>
            </div>

            <div style="background: #FFFFFF; border-radius: 12px; padding: 20px; margin: 20px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
                <h4 style="color: #1A365D; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">📅 Giveaway Timeline</h4>
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div style="text-align: center;">
                        <div style="background: #38A169; color: white; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; margin: 0 auto 8px; font-size: 18px;">✓</div>
                        <p style="margin: 0; color: #38A169; font-weight: 600; font-size: 14px;">ENTERED</p>
                        <p style="margin: 0; color: #4A5568; font-size: 12px;">You're in!</p>
                    </div>
                    <div style="flex: 1; height: 2px; background: linear-gradient(90deg, #38A169, #F6AD55); margin: 0 10px;"></div>
                    <div style="text-align: center;">
                        <div style="background: #F6AD55; color: white; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; margin: 0 auto 8px; font-size: 18px;">🎯</div>
                        <p style="margin: 0; color: #F6AD55; font-weight: 600; font-size: 14px;">DRAWING</p>
                        <p style="margin: 0; color: #4A5568; font-size: 12px;">{end_date}</p>
                    </div>
                    <div style="flex: 1; height: 2px; background: linear-gradient(90deg, #F6AD55, #E2E8F0); margin: 0 10px;"></div>
                    <div style="text-align: center;">
                        <div style="background: #E2E8F0; color: #A0AEC0; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; margin: 0 auto 8px; font-size: 18px;">🏆</div>
                        <p style="margin: 0; color: #A0AEC0; font-weight: 600; font-size: 14px;">WINNER</p>
                        <p style="margin: 0; color: #4A5568; font-size: 12px;">Soon!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- How It Works -->
    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 2px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; text-align: center;">🎲 How the Giveaway Works</h4>

        <div style="display: grid; gap: 15px;">
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #4299E1;">
                <div style="background: #4299E1; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">1</div>
                <div>
                    <strong style="color: #1A365D;">Automatic Entry</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Your $50K+ account purchase automatically entered you</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #38A169;">
                <div style="background: #38A169; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">2</div>
                <div>
                    <strong style="color: #1A365D;">Fair Selection</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Winner will be randomly selected on the end date</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #F6AD55;">
                <div style="background: #F6AD55; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">3</div>
                <div>
                    <strong style="color: #1A365D;">Instant Notification</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Winner will be notified immediately via email</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Increase Your Chances -->
    <div style="background: #F0FFF4; border: 2px solid #38A169; border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
        <div style="display: inline-block; background: #38A169; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin-bottom: 15px; font-size: 24px;">💡</div>
        <h4 style="color: #2F855A; margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">Want More Chances to Win?</h4>
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Each qualifying purchase ($50K+) gives you another entry!</p>
        <p style="margin: 0; color: #2F855A; font-weight: 600; font-size: 14px;">More accounts = More chances to win! 🚀</p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Good luck in the giveaway!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">May the best trader win! 🏆</p>
    </div>
    """
    return create_base_email_template(
        "🎁 You're Entered in Our Exclusive Giveaway!",
        content,
        "View Giveaway Details",
        "https://fxthrone.com/giveaways",
        "#F6AD55"
    )

def create_giveaway_winner_email(username, giveaway_title, prize_description):
    """
    Giveaway winner notification email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #38A169 0%, #2F855A 100%); color: white; border-radius: 50%; width: 120px; height: 120px; line-height: 120px; margin-bottom: 20px; font-size: 60px; box-shadow: 0 15px 40px rgba(56, 161, 105, 0.4); animation: pulse 2s infinite;">🏆</div>
        <h1 style="color: #1A365D; margin: 0; font-size: 36px; font-weight: 800;">CONGRATULATIONS!</h1>
        <h2 style="color: #38A169; margin: 10px 0 0 0; font-size: 24px; font-weight: 600;">YOU WON THE GIVEAWAY!</h2>
    </div>

    <p style="margin: 0 0 30px; font-size: 20px; color: #2D3748; text-align: center;">🎉 <strong>{username}</strong>, you are the lucky winner of our <strong style="color: #38A169;">{giveaway_title}</strong>! 🎉</p>

    <!-- Prize Details -->
    <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 3px solid #38A169; border-radius: 20px; padding: 40px; margin: 30px 0; text-align: center; position: relative; overflow: hidden;">
        <div style="position: absolute; top: -30px; right: -30px; width: 120px; height: 120px; background: rgba(56, 161, 105, 0.1); border-radius: 50%;"></div>
        <div style="position: absolute; bottom: -40px; left: -40px; width: 150px; height: 150px; background: rgba(56, 161, 105, 0.1); border-radius: 50%;"></div>

        <div style="position: relative; z-index: 2;">
            <h3 style="color: #1A365D; margin: 0 0 20px 0; font-size: 28px; font-weight: 700;">Your Prize</h3>

            <div style="background: #FFFFFF; border: 3px solid #38A169; border-radius: 16px; padding: 30px; margin: 25px 0; box-shadow: 0 8px 25px rgba(56, 161, 105, 0.2);">
                <div style="display: inline-block; background: #38A169; color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">💰</div>
                <h2 style="color: #1A365D; margin: 0; font-size: 32px; font-weight: 800;">{prize_description}</h2>
                <p style="margin: 15px 0 0 0; color: #4A5568; font-size: 18px;">Ready to trade with our capital!</p>
            </div>
        </div>
    </div>

    <!-- Next Steps -->
    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 2px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; text-align: center;">🚀 What Happens Next?</h4>

        <div style="display: grid; gap: 15px;">
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #4299E1;">
                <div style="background: #4299E1; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">1</div>
                <div>
                    <strong style="color: #1A365D;">Verification Process</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Our team will verify your entry and contact you within 24 hours</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #38A169;">
                <div style="background: #38A169; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">2</div>
                <div>
                    <strong style="color: #1A365D;">Account Setup</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">We'll prepare your funded account with all necessary credentials</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #F6AD55;">
                <div style="background: #F6AD55; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">3</div>
                <div>
                    <strong style="color: #1A365D;">Start Trading</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Begin trading with your new funded account immediately!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Important Notice -->
    <div style="background: #FFFBF0; border: 2px solid #F6AD55; border-radius: 12px; padding: 25px; margin: 30px 0;">
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <div style="background: #F6AD55; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 16px;">📞</div>
            <h4 style="color: #C05621; margin: 0; font-size: 18px; font-weight: 600;">Important: We'll Contact You Soon!</h4>
        </div>
        <p style="margin: 0; color: #4A5568; font-size: 16px;">Please keep an eye on your email and phone. Our team will reach out within 24 hours to arrange your prize delivery.</p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Once again, congratulations on this amazing achievement!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Welcome to the winners' circle! 🎊</p>
    </div>
    """
    return create_base_email_template(
        "🏆 WINNER! You Won Our Exclusive Giveaway!",
        content,
        "Claim Your Prize",
        "https://fundedwhales.com/giveaways/winner",
        "#38A169"
    )

def create_affiliate_commission_email(username, referred_username, commission_points, order_id, order_value, current_tier=1):
    """
    Affiliate commission notification email template
    """
    tier_badge = ""
    tier_color = "#4299E1"
    if current_tier == 1:
        tier_badge = "🥉 Bronze Affiliate"
        tier_color = "#CD7F32"
    elif current_tier == 2:
        tier_badge = "🥈 Silver Affiliate"
        tier_color = "#C0C0C0"
    elif current_tier >= 3:
        tier_badge = "🥇 Gold Affiliate"
        tier_color = "#FFD700"

    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #38A169 0%, #2F855A 100%); color: white; border-radius: 50%; width: 100px; height: 100px; line-height: 100px; margin-bottom: 20px; font-size: 50px; box-shadow: 0 10px 30px rgba(56, 161, 105, 0.3);">💰</div>
        <h2 style="color: #1A365D; margin: 0; font-size: 32px; font-weight: 800;">COMMISSION EARNED!</h2>
        <p style="margin: 10px 0 0 0; color: #38A169; font-size: 18px; font-weight: 600;">Your Affiliate Program is Working!</p>
    </div>

    <p style="margin: 0 0 30px; font-size: 18px; color: #2D3748; text-align: center;">Great news <strong>{username}</strong>! You've earned <strong style="color: #38A169;">{commission_points} points</strong> from your affiliate program!</p>

    <!-- Commission Details -->
    <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 3px solid #38A169; border-radius: 20px; padding: 35px; margin: 30px 0; text-align: center; position: relative; overflow: hidden;">
        <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(56, 161, 105, 0.1); border-radius: 50%;"></div>
        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(56, 161, 105, 0.1); border-radius: 50%;"></div>

        <div style="position: relative; z-index: 2;">
            <!-- Tier Badge -->
            <div style="background: {tier_color}; color: white; padding: 8px 20px; border-radius: 20px; display: inline-block; margin-bottom: 20px; font-weight: 600; font-size: 14px;">
                {tier_badge}
            </div>

            <h3 style="color: #1A365D; margin: 0 0 20px 0; font-size: 24px; font-weight: 700;">Commission Details</h3>

            <div style="background: #FFFFFF; border: 2px solid #38A169; border-radius: 16px; padding: 25px; margin: 25px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
                <div style="display: grid; gap: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                        <span style="color: #4A5568; font-weight: 500;">Referred User:</span>
                        <span style="color: #1A365D; font-weight: 700;">{referred_username}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                        <span style="color: #4A5568; font-weight: 500;">Order ID:</span>
                        <span style="color: #1A365D; font-weight: 700; font-family: monospace;">#{order_id}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                        <span style="color: #4A5568; font-weight: 500;">Order Value:</span>
                        <span style="color: #1A365D; font-weight: 700; font-size: 18px;">${order_value:,.0f}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                        <span style="color: #4A5568; font-weight: 500;">Commission Earned:</span>
                        <div style="text-align: right;">
                            <div style="color: #38A169; font-weight: 800; font-size: 24px;">{commission_points} Points</div>
                            <div style="color: #4A5568; font-size: 12px;">Added to your balance</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Stats -->
    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 2px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; text-align: center;">📊 Your Affiliate Performance</h4>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; text-align: center;">
            <div style="background: #FFFFFF; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                <div style="color: #4299E1; font-size: 28px; font-weight: 800; margin-bottom: 5px;">🎯</div>
                <div style="color: #1A365D; font-weight: 600; font-size: 16px;">Active Referrals</div>
                <div style="color: #4A5568; font-size: 14px;">Keep growing!</div>
            </div>
            <div style="background: #FFFFFF; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                <div style="color: #38A169; font-size: 28px; font-weight: 800; margin-bottom: 5px;">💎</div>
                <div style="color: #1A365D; font-weight: 600; font-size: 16px;">{tier_badge.split()[1]} Tier</div>
                <div style="color: #4A5568; font-size: 14px;">Current level</div>
            </div>
            <div style="background: #FFFFFF; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                <div style="color: #F6AD55; font-size: 28px; font-weight: 800; margin-bottom: 5px;">🚀</div>
                <div style="color: #1A365D; font-weight: 600; font-size: 16px;">Growing</div>
                <div style="color: #4A5568; font-size: 14px;">Keep it up!</div>
            </div>
        </div>
    </div>

    <!-- Earning More -->
    <div style="background: #FFFBF0; border: 2px solid #F6AD55; border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
        <div style="display: inline-block; background: #F6AD55; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin-bottom: 15px; font-size: 24px;">💡</div>
        <h4 style="color: #C05621; margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">Want to Earn More?</h4>
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Share your referral code with more traders and earn commission on every order they place!</p>
        <p style="margin: 0; color: #C05621; font-weight: 600; font-size: 14px;">More referrals = More commissions = Higher tier rewards! 🎯</p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Thank you for being a valued affiliate partner!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Keep up the excellent work! 🌟</p>
    </div>
    """
    return create_base_email_template(
        "💰 Affiliate Commission Earned!",
        content,
        "View Affiliate Dashboard",
        "https://fxthrone.com/affiliate/dashboard",
        "#38A169"
    )

def create_affiliate_tier_upgrade_email(username, new_tier, new_commission_rate):
    """
    Affiliate tier upgrade notification email template
    """
    tier_info = {
        1: {"name": "Bronze", "emoji": "🥉", "color": "#CD7F32"},
        2: {"name": "Silver", "emoji": "🥈", "color": "#C0C0C0"},
        3: {"name": "Gold", "emoji": "🥇", "color": "#FFD700"}
    }

    tier = tier_info.get(new_tier, tier_info[1])

    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, {tier['color']} 0%, #F6AD55 100%); color: white; border-radius: 50%; width: 120px; height: 120px; line-height: 120px; margin-bottom: 20px; font-size: 60px; box-shadow: 0 15px 40px rgba(246, 173, 85, 0.4);">{tier['emoji']}</div>
        <h1 style="color: #1A365D; margin: 0; font-size: 36px; font-weight: 800;">TIER UPGRADE!</h1>
        <h2 style="color: {tier['color']}; margin: 10px 0 0 0; font-size: 24px; font-weight: 600;">Welcome to {tier['name']} Level!</h2>
    </div>

    <p style="margin: 0 0 30px; font-size: 20px; color: #2D3748; text-align: center;">🎉 Congratulations <strong>{username}</strong>! You've been upgraded to <strong style="color: {tier['color']};">{tier['name']} Affiliate</strong> status! 🎉</p>

    <!-- New Benefits -->
    <div style="background: linear-gradient(135deg, #FFFBF0 0%, #FEF5E7 100%); border: 3px solid {tier['color']}; border-radius: 20px; padding: 40px; margin: 30px 0; text-align: center;">
        <h3 style="color: #1A365D; margin: 0 0 25px 0; font-size: 28px; font-weight: 700;">Your New Benefits</h3>

        <div style="background: #FFFFFF; border: 2px solid {tier['color']}; border-radius: 16px; padding: 30px; margin: 25px 0;">
            <div style="display: inline-block; background: {tier['color']}; color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">💎</div>
            <h2 style="color: #1A365D; margin: 0; font-size: 32px; font-weight: 800;">{new_commission_rate} Points</h2>
            <p style="margin: 10px 0 0 0; color: #4A5568; font-size: 18px;">Per order commission (increased!)</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;">
            <div style="background: #FFFFFF; border-radius: 12px; padding: 25px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
                <div style="color: {tier['color']}; font-size: 32px; margin-bottom: 10px;">⚡</div>
                <h4 style="color: #1A365D; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">Higher Commissions</h4>
                <p style="margin: 0; color: #4A5568; font-size: 14px;">Earn more points per order</p>
            </div>
            <div style="background: #FFFFFF; border-radius: 12px; padding: 25px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
                <div style="color: {tier['color']}; font-size: 32px; margin-bottom: 10px;">🎯</div>
                <h4 style="color: #1A365D; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">Priority Support</h4>
                <p style="margin: 0; color: #4A5568; font-size: 14px;">Dedicated affiliate support</p>
            </div>
            <div style="background: #FFFFFF; border-radius: 12px; padding: 25px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
                <div style="color: {tier['color']}; font-size: 32px; margin-bottom: 10px;">🏆</div>
                <h4 style="color: #1A365D; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">Exclusive Bonuses</h4>
                <p style="margin: 0; color: #4A5568; font-size: 14px;">Special tier rewards</p>
            </div>
        </div>
    </div>

    <!-- Next Tier Preview -->
    {f'''
    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 2px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0; text-align: center;">
        <h4 style="color: #1A365D; margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">🚀 Next Level Preview</h4>
        <p style="margin: 0; color: #4A5568; font-size: 16px;">Keep growing to unlock even higher commission rates and exclusive benefits!</p>
    </div>
    ''' if new_tier < 3 else ''}

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">You've earned this upgrade through your dedication and success!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Welcome to the {tier['name']} level! 🎊</p>
    </div>
    """
    return create_base_email_template(
        f"🏆 {tier['emoji']} Affiliate Tier Upgrade - {tier['name']} Level!",
        content,
        "View Your Dashboard",
        "https://fundedwhales.com/affiliate/dashboard",
        tier['color']
    )

def create_order_confirmation_email(username, order_id, challenge_type, account_size, platform):
    """
    Modern order confirmation email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #38A169 0%, #2F855A 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">✅</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Order Confirmed!</h3>
    </div>

    <p style="margin: 0 0 30px; font-size: 18px; color: #2D3748; text-align: center;">Thank you <strong>{username}</strong>! Your trading challenge order has been <strong style="color: #38A169;">successfully processed</strong>.</p>

    <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 2px solid #38A169; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 25px 0; font-size: 20px; font-weight: 600; text-align: center;">📋 Order Details</h4>

        <div style="background: #FFFFFF; border-radius: 12px; padding: 25px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
            <div style="display: grid; gap: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                    <span style="color: #4A5568; font-weight: 500;">Order Number:</span>
                    <span style="color: #1A365D; font-weight: 700; font-family: monospace;">FxE{order_id}</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                    <span style="color: #4A5568; font-weight: 500;">Challenge Type:</span>
                    <span style="color: #FF6B35; font-weight: 700;">{challenge_type}</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #E2E8F0;">
                    <span style="color: #4A5568; font-weight: 500;">Account Size:</span>
                    <span style="color: #1A365D; font-weight: 700; font-size: 18px;">{account_size}</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                    <span style="color: #4A5568; font-weight: 500;">Platform:</span>
                    <span style="color: #1A365D; font-weight: 700;">{platform}</span>
                </div>
            </div>
        </div>
    </div>

    <div style="background: #FFF5F5; border: 1px solid #FEB2B2; border-radius: 12px; padding: 25px; margin: 30px 0;">
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <div style="background: #E53E3E; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 16px;">⏱</div>
            <h4 style="color: #C53030; margin: 0; font-size: 18px; font-weight: 600;">What Happens Next?</h4>
        </div>
        <div style="margin-left: 45px;">
            <p style="margin: 0 0 10px 0; color: #4A5568; font-size: 15px;">• Our team is preparing your trading challenge account</p>
            <p style="margin: 0 0 10px 0; color: #4A5568; font-size: 15px;">• You'll receive login credentials within 24 hours</p>
            <p style="margin: 0; color: #4A5568; font-size: 15px;">• Check your email for account setup notification</p>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Questions about your order?</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Our support team is here to help! 🚀</p>
    </div>
    """
    return create_base_email_template(
        "Order Confirmation - FxThrone",
        content,
        "Track Your Order",
        f"https://fxthrone.com/orders/{order_id}",
        "#38A169"
    )

def create_challenge_completion_email(username, order_id, server, login, password):
    """
    Challenge completion email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <p style="margin: 0 0 20px;">Your trading challenge account has been successfully set up and is ready for trading!</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Your Trading Account Credentials:</h4>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px 0; color: #718096;">Server:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{server}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Login:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{login}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Password:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{password}</td>
            </tr>
        </table>
    </div>
    
    <p style="margin: 20px 0;">Please make sure to read our trading rules and risk management guidelines before starting your challenge.</p>
    """
    return create_base_email_template(
        "Your Challenge Account is Ready",
        content,
        "Start Trading Now",
        f"https://fundedwhales.com/dashboard/challenge/{order_id}"
    )

def create_pass_notification_email(username, order_id, profit_amount=None):
    """
    Modern challenge pass notification email template
    """
    profit_section = ""
    if profit_amount:
        profit_section = f"""
        <div style="background: linear-gradient(135deg, #F0FFF4 0%, #E6FFFA 100%); border: 2px solid #38A169; border-radius: 12px; padding: 20px; margin: 25px 0; text-align: center;">
            <h4 style="color: #38A169; margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">💰 Total Profit Achieved</h4>
            <div style="background: #FFFFFF; border: 2px dashed #38A169; border-radius: 8px; padding: 15px;">
                <h3 style="color: #1A365D; margin: 0; font-size: 32px; font-weight: 800;">${profit_amount}</h3>
            </div>
        </div>
        """

    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #38A169 0%, #2F855A 100%); color: white; border-radius: 50%; width: 100px; height: 100px; line-height: 100px; margin-bottom: 20px; font-size: 50px; box-shadow: 0 10px 30px rgba(56, 161, 105, 0.3);">🏆</div>
        <h2 style="color: #1A365D; margin: 0; font-size: 32px; font-weight: 800;">CONGRATULATIONS!</h2>
        <p style="margin: 10px 0 0 0; color: #38A169; font-size: 20px; font-weight: 600;">Challenge Passed Successfully!</p>
    </div>

    <p style="margin: 0 0 30px; font-size: 18px; color: #2D3748; text-align: center;">Outstanding work <strong>{username}</strong>! You've successfully completed your trading challenge and proven your skills.</p>

    {profit_section}

    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 2px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <h4 style="color: #1A365D; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; text-align: center;">🚀 What Happens Next?</h4>

        <div style="display: grid; gap: 15px;">
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #4299E1;">
                <div style="background: #4299E1; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">1</div>
                <div>
                    <strong style="color: #1A365D;">Performance Review</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Our team will analyze your trading performance</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #38A169;">
                <div style="background: #38A169; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">2</div>
                <div>
                    <strong style="color: #1A365D;">Account Preparation</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">We'll set up your funded trading account</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #FF6B35;">
                <div style="background: #FF6B35; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">3</div>
                <div>
                    <strong style="color: #1A365D;">Credentials Delivery</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">You'll receive your funded account login details</p>
                </div>
            </div>
        </div>
    </div>

    <div style="background: #FFFBF0; border: 2px solid #F6AD55; border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
        <div style="display: inline-block; background: #F6AD55; color: white; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; margin-bottom: 15px; font-size: 20px;">⏰</div>
        <h4 style="color: #C05621; margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">Expected Timeline</h4>
        <p style="margin: 0; color: #4A5568; font-size: 16px;">You'll receive your funded account details within <strong>24-48 hours</strong></p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Welcome to the elite group of funded traders!</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Your trading journey just got exciting! 🎉</p>
    </div>
    """
    return create_base_email_template(
        "🏆 Challenge Passed - You're Now a Funded Trader!",
        content,
        "View Your Results",
        f"https://fxthrone.com/dashboard/challenge/{order_id}/results",
        "#38A169"
    )

def create_fail_notification_email(username, order_id, reason):
    """
    Challenge fail notification email template
    """
    content = f"""
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%); color: white; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; margin-bottom: 20px; font-size: 40px;">📊</div>
        <h3 style="color: #1A365D; margin: 0; font-size: 24px; font-weight: 700;">Challenge Update</h3>
    </div>

    <p style="margin: 0 0 30px; font-size: 18px; color: #2D3748; text-align: center;">Hi <strong>{username}</strong>, we have an update regarding your trading challenge.</p>

    <div style="background: linear-gradient(135deg, #FFF5F5 0%, #FED7D7 100%); border: 2px solid #E53E3E; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <div style="display: flex; align-items: center; margin-bottom: 20px;">
            <div style="background: #E53E3E; color: white; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; text-align: center; margin-right: 15px; font-size: 20px;">⚠</div>
            <h4 style="color: #C53030; margin: 0; font-size: 20px; font-weight: 600;">Challenge Status: Not Passed</h4>
        </div>

        <div style="background: #FFFFFF; border: 1px solid #E53E3E; border-radius: 8px; padding: 20px; margin: 15px 0;">
            <h5 style="color: #1A365D; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">Reason:</h5>
            <p style="margin: 0; color: #4A5568; font-size: 15px; line-height: 1.5;">{reason}</p>
        </div>
    </div>

    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #BEE3F8 100%); border: 2px solid #4299E1; border-radius: 16px; padding: 30px; margin: 30px 0;">
        <div style="text-align: center; margin-bottom: 20px;">
            <div style="display: inline-block; background: #4299E1; color: white; border-radius: 50%; width: 50px; height: 50px; line-height: 50px; margin-bottom: 15px; font-size: 24px;">💪</div>
            <h4 style="color: #1A365D; margin: 0; font-size: 20px; font-weight: 600;">Don't Give Up!</h4>
        </div>

        <p style="margin: 0 0 20px 0; color: #2D3748; text-align: center; font-size: 16px;">Every successful trader has faced setbacks. This is part of your learning journey!</p>

        <div style="display: grid; gap: 15px;">
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #4299E1;">
                <div style="background: #4299E1; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">1</div>
                <div>
                    <strong style="color: #1A365D;">Analyze & Learn</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Review your trades and identify areas for improvement</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #38A169;">
                <div style="background: #38A169; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">2</div>
                <div>
                    <strong style="color: #1A365D;">Practice More</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Refine your strategy on demo accounts</p>
                </div>
            </div>
            <div style="display: flex; align-items: center; padding: 15px; background: #FFFFFF; border-radius: 8px; border-left: 4px solid #FF6B35;">
                <div style="background: #FF6B35; color: white; border-radius: 50%; width: 30px; height: 30px; line-height: 30px; text-align: center; margin-right: 15px; font-size: 14px; font-weight: bold;">3</div>
                <div>
                    <strong style="color: #1A365D;">Try Again</strong>
                    <p style="margin: 5px 0 0 0; color: #4A5568; font-size: 14px;">Start a new challenge when you're ready</p>
                </div>
            </div>
        </div>
    </div>

    <div style="background: #FFFBF0; border: 2px solid #F6AD55; border-radius: 12px; padding: 25px; margin: 30px 0; text-align: center;">
        <div style="display: inline-block; background: #F6AD55; color: white; border-radius: 50%; width: 40px; height: 40px; line-height: 40px; margin-bottom: 15px; font-size: 20px;">🎯</div>
        <h4 style="color: #C05621; margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">Special Offer</h4>
        <p style="margin: 0; color: #4A5568; font-size: 16px;">Get <strong>20% off</strong> your next challenge - Use code: <strong style="color: #C05621;">COMEBACK20</strong></p>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="margin: 0 0 15px 0; color: #4A5568; font-size: 16px;">Remember: Every expert was once a beginner.</p>
        <p style="margin: 0; color: #1A365D; font-weight: 600; font-size: 18px;">Your comeback story starts now! 🚀</p>
    </div>
    """
    return create_base_email_template(
        "Challenge Update - Let's Get You Back on Track!",
        content,
        "Start New Challenge",
        "https://fxthrone.com/challenges",
        "#4299E1"
    )

def create_live_account_email(username, order_id, server, login, password, profit_share):
    """
    Live account creation email template
    """
    content = f"""
    <h3 style="color: #2C5282; margin: 0 0 20px; font-size: 20px;">Dear {username},</h3>
    
    <div style="text-align: center; margin: 30px 0;">
        <div style="display: inline-block; background: #F0FFF4; border: 2px solid #48BB78; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 20px;">
            <span style="color: #48BB78; font-size: 40px;">🌟</span>
        </div>
        <h2 style="color: #48BB78; margin: 0;">Welcome to the Funded Traders Club!</h2>
    </div>
    
    <p style="margin: 0 0 20px;">Your live trading account has been successfully created. Here are your account details:</p>
    
    <div style="background: #F7FAFC; border: 1px solid #E2E8F0; border-radius: 8px; padding: 20px; margin: 25px 0;">
        <h4 style="color: #2C5282; margin: 0 0 15px;">Live Account Credentials:</h4>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px 0; color: #718096;">Server:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{server}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Login:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{login}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Password:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{password}</td>
            </tr>
            <tr>
                <td style="padding: 10px 0; color: #718096;">Profit Share:</td>
                <td style="padding: 10px 0; color: #2C5282; font-weight: 600;">{profit_share}%</td>
            </tr>
        </table>
    </div>
    
    <p style="margin: 20px 0;">Remember to follow our trading rules and risk management guidelines. Happy trading!</p>
    """
    return create_base_email_template(
        "Your Live Account is Ready",
        content,
        "Start Live Trading",
        f"https://fxthrone.com/dashboard/live/{order_id}"
    )

def create_certificate_email(username, order_id, challenge_type, account_size, certificate_id, issue_date, profit_amount=None):
    """
    Advanced certificate email template with luxury design
    """
    content = f"""
    <div style="background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%); border-radius: 15px; padding: 3px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%); border-radius: 12px; padding: 40px; position: relative; overflow: hidden;">
            <!-- Decorative Elements -->
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNMCAwbDUwIDUwTDAgMTAwIiBzdHJva2U9InJnYmEoNzQsIDE0NCwgMjI2LCAwLjEpIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4=') repeat; opacity: 0.1;"></div>
            
            <!-- Certificate Header -->
            <div style="text-align: center; position: relative; z-index: 1;">
                <div style="margin-bottom: 30px;">
                    <h1 style="color: #4A90E2; margin: 0; font-size: 36px; letter-spacing: 3px; text-transform: uppercase; font-weight: 800; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">Certificate of Achievement</h1>
                    <div style="width: 100px; height: 3px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 15px auto;"></div>
                    <p style="color: #2d3748; font-size: 18px; margin: 10px 0;">FundedWhales Trading Excellence</p>
                </div>
                
                <!-- Recipient Info -->
                <div style="margin: 40px 0;">
                    <p style="color: #4a5568; font-size: 16px; margin: 0;">This certificate is proudly presented to</p>
                    <h2 style="color: #2d3748; font-size: 32px; margin: 15px 0; font-family: 'Playfair Display', serif;">{username}</h2>
                    <p style="color: #4a5568; font-size: 16px; line-height: 1.6; max-width: 600px; margin: 20px auto;">
                        For demonstrating exceptional trading proficiency and successfully completing the FxThrone Trading Challenge
                        with outstanding performance and adherence to risk management principles.
                    </p>
                </div>
                
                <!-- Achievement Details -->
                <div style="background: linear-gradient(135deg, #EBF8FF 0%, #F0F7FF 100%); border: 1px solid rgba(74, 144, 226, 0.2); border-radius: 12px; padding: 25px; margin: 30px auto; max-width: 500px; box-shadow: 0 4px 6px rgba(74, 144, 226, 0.1);">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Challenge Type:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #2d3748; font-weight: 600;">{challenge_type}</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Account Size:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #2d3748; font-weight: 600;">{account_size}</td>
                        </tr>
                        {f'''
                        <tr>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #4a5568;">Total Profit:</td>
                            <td style="padding: 12px; border-bottom: 1px solid rgba(74, 144, 226, 0.1); color: #48BB78; font-weight: 600;">${profit_amount}</td>
                        </tr>
                        ''' if profit_amount else ''}
                        <tr>
                            <td style="padding: 12px; color: #4a5568;">Certificate ID:</td>
                            <td style="padding: 12px; color: #2d3748; font-weight: 600;">{certificate_id}</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Signatures -->
                <div style="display: flex; justify-content: space-between; margin-top: 40px; flex-wrap: wrap;">
                    <div style="flex: 1; min-width: 200px; text-align: center; margin: 20px;">
                        <div style="font-family: 'Dancing Script', cursive; color: #2d3748; font-size: 28px;">Maxwell Grant</div>
                        <div style="width: 80%; height: 2px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 10px auto;"></div>
                        <p style="color: #4a5568; margin: 5px 0 0; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Chief Executive Officer</p>
                    </div>
                    <div style="flex: 1; min-width: 200px; text-align: center; margin: 20px;">
                        <div style="font-family: 'Montserrat', sans-serif; color: #2d3748; font-size: 18px;">{issue_date}</div>
                        <div style="width: 80%; height: 2px; background: linear-gradient(90deg, transparent, #4A90E2, transparent); margin: 10px auto;"></div>
                        <p style="color: #4a5568; margin: 5px 0 0; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Date of Issue</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    """
    return create_base_email_template(
        "Trading Excellence Certificate",
        content,
        "View Certificate",
        f"https://fundedwhales.com/certificates/{certificate_id}"
    ) 