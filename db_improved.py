from sqlmodel import SQLModel, Session, create_engine
from sqlalchemy.pool import <PERSON>ull<PERSON><PERSON>
import os
import time
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Ensure SSL mode is set for Neon database
if "sslmode=" not in database_url:
    separator = "&" if "?" in database_url else "?"
    database_url += f"{separator}sslmode=require"

def create_engine_with_retry(database_url, max_retries=3, retry_delay=5):
    """Create database engine with retry logic and better error handling"""
    for attempt in range(max_retries):
        try:
            print(f"🔄 Database connection attempt {attempt + 1}/{max_retries}")
            
            # Create engine with better connection settings
            engine = create_engine(
                database_url,
                echo=False,  # Disable SQL logging for cleaner output
                pool_pre_ping=True,  # Verify connections before use
                pool_recycle=3600,   # Recycle connections every hour
                poolclass=NullPool,  # Disable connection pooling for better reliability
                connect_args={
                    "connect_timeout": 30,
                    "application_name": "FundedWhales_App",
                    "sslmode": "require"
                }
            )
            
            # Test the connection
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            
            print(f"✅ Database connection successful!")
            return engine
            
        except Exception as e:
            print(f"❌ Connection attempt {attempt + 1} failed: {e}")
            
            if "could not translate host name" in str(e):
                print("🌐 DNS resolution issue detected. Possible solutions:")
                print("   1. Check your internet connection")
                print("   2. Try changing DNS servers to *******, *******")
                print("   3. Flush DNS cache: ipconfig /flushdns")
                print("   4. Check if firewall is blocking the connection")
                print("   5. Try using mobile hotspot or different network")
            
            if attempt < max_retries - 1:
                print(f"⏳ Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                print("❌ All database connection attempts failed")
                print("💡 Please check:")
                print("   - Internet connectivity")
                print("   - DATABASE_URL is correct")
                print("   - Neon database service status")
                raise e

# Create the engine with retry logic
try:
    engine = create_engine_with_retry(database_url)
except Exception as e:
    print(f"🚨 Critical: Cannot establish database connection: {e}")
    print("🔧 The application will continue but database operations will fail")
    # Create a dummy engine to prevent import errors
    engine = create_engine("sqlite:///dummy.db", echo=False)

def create_db_and_tables():
    """Create database tables based on SQLModel definitions."""
    # Import all models to ensure they're registered with SQLModel metadata
    # This import is used to register the model with SQLModel metadata
    # pylint: disable=unused-import
    from routes.account import AccountCredential
    from models.user import User
    from models.points_transaction import PointsTransaction
    from models.user_verification import UserVerification
    from models.referral_access import ReferralCodeAccess
    from models.order import OrderModel, OrderImage, DateTimeModel, PassOrder, OrderTimeline, CompleteOrderModel, Stage2Account, LiveAccount, RejectOrder, FailOrder, Certificate
    from models.giveaway import Giveaway, GiveawayEntry, GiveawayWinner
    from models.affiliate import AffiliateSettings, AffiliateUser, AffiliateCommission, AffiliatePayment, AffiliateLink

    # Create all tables
    SQLModel.metadata.create_all(engine)

    # Explicitly create the account_credentials table
    try:
        AccountCredential.create_table(engine)
        print("Ensured account_credentials table exists")
    except Exception as e:
        print(f"Error ensuring account_credentials table: {str(e)}")

    # Add created_at column to ordermodel table if it doesn't exist
    try:
        with Session(engine) as session:
            # Check if the column exists
            from sqlalchemy import text
            result = session.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name='ordermodel' AND column_name='created_at';
            """))

            if not result.fetchone():
                print("Adding created_at column to ordermodel table...")
                session.execute(text("""
                    ALTER TABLE ordermodel
                    ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                """))
                session.commit()
                print("created_at column added successfully.")
            else:
                print("created_at column already exists in ordermodel table.")
    except Exception as e:
        print(f"Error checking/adding created_at column: {str(e)}")

def get_session():
    """Provide a database session using a context manager."""
    with Session(engine) as session:
        yield session
